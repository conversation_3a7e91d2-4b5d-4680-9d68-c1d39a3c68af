# Environment configuration for InfraOps Agent
# Copy this file to .env and customize the values

# Application settings
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# AWS Configuration
# AWS_ACCESS_KEY_ID=your_access_key_here
# AWS_SECRET_ACCESS_KEY=your_secret_key_here
# AWS_DEFAULT_REGION=us-west-2

# Database Configuration (for Docker Compose)
POSTGRES_DB=infraops
POSTGRES_USER=infraops
POSTGRES_PASSWORD=infraops_dev_password
DATABASE_URL=*********************************************************/infraops

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# SSH Configuration
SSH_KEY_PATH=~/.ssh/infraops_key
SSH_PUBLIC_KEY_PATH=~/.ssh/infraops_key.pub

# Security Configuration
ALLOWED_SSH_CIDRS=["0.0.0.0/0"]  # CHANGE THIS FOR PRODUCTION!
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Monitoring Configuration
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
GRAFANA_ADMIN_PASSWORD=infraops-admin

# Jupyter Configuration
JUPYTER_TOKEN=infraops-dev-token

# AI/LLM Configuration (optional)
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Notification Configuration (optional)
# SLACK_WEBHOOK_URL=your_slack_webhook_url_here
# EMAIL_SMTP_SERVER=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_USERNAME=<EMAIL>
# EMAIL_PASSWORD=your_app_password_here

# Cost Management
COST_ALERT_THRESHOLD=100.00  # Alert when monthly cost exceeds this amount
COST_TRACKING_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=infraops-backups-your-unique-suffix

# Feature Flags
ENABLE_WEB_UI=false
ENABLE_API=false
ENABLE_COST_TRACKING=true
ENABLE_MONITORING=true
