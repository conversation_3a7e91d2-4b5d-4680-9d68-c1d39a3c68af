#!/usr/bin/env python3
"""Test the dashboard API endpoints"""

import requests
import json

def test_api():
    base_url = "http://localhost:8001"
    
    endpoints = [
        "/api/status",
        "/api/metrics", 
        "/api/instances"
    ]
    
    print("🧪 Testing InfraOps Dashboard API")
    print("=" * 40)
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            print(f"\n📡 Testing {endpoint}")
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: {json.dumps(data, indent=2)}")
            else:
                print(f"❌ Failed: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error: {e}")
    
    print(f"\n🌐 Dashboard URL: {base_url}")

if __name__ == "__main__":
    test_api()
