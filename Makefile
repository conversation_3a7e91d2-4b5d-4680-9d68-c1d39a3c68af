# Makefile for InfraOps Agent
# Provides convenient commands for development and deployment

.PHONY: help install setup build up down logs shell test clean deploy destroy

# Default target
help: ## Show this help message
	@echo "InfraOps AI Automation Agent"
	@echo "============================="
	@echo ""
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Installation and setup
install: ## Install Python dependencies
	pip install -r requirements.txt

setup: ## Initial setup - copy example files and create directories
	@echo "Setting up InfraOps Agent..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
	@if [ ! -f terraform/live/dev/terraform.tfvars ]; then cp terraform/live/dev/terraform.tfvars.example terraform/live/dev/terraform.tfvars; echo "Created terraform.tfvars from template"; fi
	@mkdir -p artifacts logs
	@echo "Setup complete! Please edit .env and terraform.tfvars files with your configuration."

# Docker operations
build: ## Build Docker images
	docker-compose build

up: ## Start all services
	docker-compose up -d

up-dev: ## Start services for development (with logs)
	docker-compose up

up-monitoring: ## Start services with monitoring stack
	docker-compose --profile monitoring up -d

up-testing: ## Start services with testing stack (LocalStack)
	docker-compose --profile testing up -d

down: ## Stop all services
	docker-compose down

down-volumes: ## Stop all services and remove volumes
	docker-compose down -v

logs: ## Show logs from all services
	docker-compose logs -f

logs-agent: ## Show logs from InfraOps agent only
	docker-compose logs -f infraops-agent

shell: ## Open shell in the agent container
	docker-compose exec infraops-agent bash

shell-root: ## Open root shell in the agent container
	docker-compose exec -u root infraops-agent bash

# Development operations
test: ## Run tests
	python -m pytest tests/ -v

test-coverage: ## Run tests with coverage
	python -m pytest tests/ --cov=pipeline --cov-report=html

lint: ## Run code linting
	flake8 pipeline/ agent/
	black --check pipeline/ agent/

format: ## Format code
	black pipeline/ agent/

validate-terraform: ## Validate Terraform configuration
	cd terraform/live/dev && terraform validate

validate-ansible: ## Validate Ansible playbooks
	ansible-playbook --syntax-check ansible/playbooks/*.yml

# Infrastructure operations
plan: ## Run Terraform plan
	cd terraform/live/dev && terraform plan

apply: ## Run Terraform apply
	cd terraform/live/dev && terraform apply

destroy-infra: ## Destroy Terraform infrastructure
	cd terraform/live/dev && terraform destroy

# Pipeline operations
run-dev: ## Run pipeline for development environment
	python pipeline/run.py --env dev --prompt "Create a web server with NGINX and Docker"

run-dev-auto: ## Run pipeline for development with auto-approve
	python pipeline/run.py --env dev --prompt "Create a web server with NGINX and Docker" --auto-approve

run-staging: ## Run pipeline for staging environment
	python pipeline/run.py --env staging --prompt "Create a staging web server"

# Utility operations
clean: ## Clean up temporary files and caches
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/

clean-docker: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

backup-state: ## Backup Terraform state
	@if [ -f terraform/live/dev/terraform.tfstate ]; then \
		cp terraform/live/dev/terraform.tfstate terraform/live/dev/terraform.tfstate.backup.$(shell date +%Y%m%d_%H%M%S); \
		echo "Terraform state backed up"; \
	else \
		echo "No Terraform state file found"; \
	fi

# Monitoring operations
prometheus: ## Open Prometheus in browser
	@echo "Opening Prometheus at http://localhost:9090"
	@python -c "import webbrowser; webbrowser.open('http://localhost:9090')"

grafana: ## Open Grafana in browser
	@echo "Opening Grafana at http://localhost:3000 (admin/infraops-admin)"
	@python -c "import webbrowser; webbrowser.open('http://localhost:3000')"

jupyter: ## Open Jupyter Lab in browser
	@echo "Opening Jupyter Lab at http://localhost:8888 (token: infraops-dev-token)"
	@python -c "import webbrowser; webbrowser.open('http://localhost:8888/lab?token=infraops-dev-token')"

# Security operations
check-secrets: ## Check for potential secrets in code
	@echo "Checking for potential secrets..."
	@grep -r -i "password\|secret\|key\|token" --include="*.py" --include="*.yml" --include="*.yaml" . | grep -v ".git" | grep -v "example" | grep -v "template" || echo "No secrets found"

generate-ssh-key: ## Generate SSH key for infrastructure access
	@if [ ! -f ~/.ssh/infraops_key ]; then \
		ssh-keygen -t rsa -b 4096 -f ~/.ssh/infraops_key -N "" -C "infraops-agent"; \
		echo "SSH key generated: ~/.ssh/infraops_key"; \
	else \
		echo "SSH key already exists: ~/.ssh/infraops_key"; \
	fi

# Documentation
docs: ## Generate documentation
	@echo "Generating documentation..."
	@python -c "import pipeline.run; help(pipeline.run)" > docs/pipeline_help.txt
	@echo "Documentation generated in docs/"

# Quick start
quickstart: setup generate-ssh-key build up ## Complete setup and start services
	@echo ""
	@echo "🚀 InfraOps Agent is ready!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit .env file with your AWS credentials"
	@echo "2. Edit terraform/live/dev/terraform.tfvars with your configuration"
	@echo "3. Run 'make run-dev' to test the pipeline"
	@echo ""
	@echo "Useful URLs:"
	@echo "- Jupyter Lab: http://localhost:8888/lab?token=infraops-dev-token"
	@echo "- Grafana: http://localhost:3000 (admin/infraops-admin)"
	@echo "- Prometheus: http://localhost:9090"
