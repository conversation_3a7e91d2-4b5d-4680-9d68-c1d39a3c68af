# InfraOps Agent Setup Guide

This guide will walk you through setting up the InfraOps AI Automation Agent on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.8+** - [Download Python](https://python.org/downloads/)
- **Docker & Docker Compose** - [Install Docker](https://docs.docker.com/get-docker/)
- **Terraform >= 1.0** - [Install Terraform](https://terraform.io/downloads)
- **Ansible >= 4.0** - Will be installed via pip
- **AWS CLI** - [Install AWS CLI](https://aws.amazon.com/cli/)
- **Git** - [Install Git](https://git-scm.com/downloads)

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd infraops

# Run the quick setup
make quickstart
```

This will:
- Copy example configuration files
- Generate SSH keys
- Build Docker images
- Start all services

### 2. Configure AWS Credentials

```bash
# Configure AWS CLI
aws configure

# Or set environment variables
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-west-2
```

### 3. Customize Configuration

Edit the configuration files:

```bash
# Edit environment variables
nano .env

# Edit Terraform variables
nano terraform/live/dev/terraform.tfvars
```

### 4. Test the Pipeline

```bash
# Run a test deployment
make run-dev

# Or with auto-approval (skip confirmation prompts)
make run-dev-auto
```

## Detailed Setup

### Step 1: Environment Configuration

Copy and edit the environment file:

```bash
cp .env.example .env
```

Key settings to configure:

```bash
# AWS Configuration
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-west-2

# SSH Configuration
SSH_KEY_PATH=~/.ssh/infraops_key
SSH_PUBLIC_KEY_PATH=~/.ssh/infraops_key.pub

# Security (IMPORTANT!)
ALLOWED_SSH_CIDRS=["YOUR_IP_ADDRESS/32"]  # Replace with your IP
```

### Step 2: SSH Key Setup

Generate SSH keys for server access:

```bash
# Generate SSH key pair
make generate-ssh-key

# Or manually:
ssh-keygen -t rsa -b 4096 -f ~/.ssh/infraops_key -N ""
```

### Step 3: Terraform Configuration

Copy and edit the Terraform variables:

```bash
cp terraform/live/dev/terraform.tfvars.example terraform/live/dev/terraform.tfvars
```

Update the following in `terraform.tfvars`:

```hcl
# Your AWS region
region = "us-west-2"

# SSH key path
ssh_public_key_path = "~/.ssh/infraops_key.pub"

# IMPORTANT: Replace with your IP address for security
allowed_ssh_cidrs = ["YOUR_IP_ADDRESS/32"]

# Instance configuration
instance_type = "t3.micro"  # Free tier eligible
```

### Step 4: Validate Configuration

```bash
# Validate Terraform configuration
make validate-terraform

# Validate Ansible playbooks
make validate-ansible

# Check for potential security issues
make check-secrets
```

## Development Setup

### Using Docker (Recommended)

```bash
# Start development environment
make up-dev

# View logs
make logs

# Open shell in container
make shell

# Stop services
make down
```

### Local Development

```bash
# Install Python dependencies
make install

# Run pipeline locally
python pipeline/run.py --env dev --prompt "Create a web server"
```

### Development Tools

Access development tools:

```bash
# Open Jupyter Lab for experimentation
make jupyter
# URL: http://localhost:8888/lab?token=infraops-dev-token

# Open Grafana for monitoring
make grafana
# URL: http://localhost:3000 (admin/infraops-admin)

# Open Prometheus for metrics
make prometheus
# URL: http://localhost:9090
```

## Testing

### Unit Tests

```bash
# Run tests
make test

# Run tests with coverage
make test-coverage
```

### Integration Tests

```bash
# Start testing environment with LocalStack
make up-testing

# Run integration tests
python -m pytest tests/integration/ -v
```

### Manual Testing

```bash
# Test with different prompts
python pipeline/run.py --env dev --prompt "Create a web server with NGINX"
python pipeline/run.py --env dev --prompt "Deploy Docker and monitoring tools"
python pipeline/run.py --env dev --prompt "Set up a development environment"
```

## Security Configuration

### SSH Access

1. **Generate secure SSH keys:**
   ```bash
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/infraops_key
   chmod 600 ~/.ssh/infraops_key
   ```

2. **Restrict SSH access to your IP:**
   ```bash
   # Get your public IP
   curl ifconfig.me

   # Update terraform.tfvars
   allowed_ssh_cidrs = ["YOUR_IP/32"]
   ```

### AWS Security

1. **Use IAM roles with minimal permissions**
2. **Enable MFA on your AWS account**
3. **Regularly rotate access keys**
4. **Monitor AWS CloudTrail logs**

### Application Security

1. **Change default passwords:**
   ```bash
   # Update .env file
   POSTGRES_PASSWORD=your_secure_password
   GRAFANA_ADMIN_PASSWORD=your_secure_password
   ```

2. **Use environment-specific configurations**
3. **Enable encryption for sensitive data**

## Troubleshooting

### Common Issues

1. **Terraform authentication errors:**
   ```bash
   # Check AWS credentials
   aws sts get-caller-identity
   
   # Verify region
   aws configure get region
   ```

2. **SSH connection failures:**
   ```bash
   # Check SSH key permissions
   ls -la ~/.ssh/infraops_key
   
   # Should be 600 or 400
   chmod 600 ~/.ssh/infraops_key
   ```

3. **Docker issues:**
   ```bash
   # Clean up Docker resources
   make clean-docker
   
   # Rebuild images
   make build
   ```

4. **Ansible connection errors:**
   ```bash
   # Test connectivity
   ansible all -i ansible/inventories/inventory.ini -m ping
   
   # Check inventory file
   cat ansible/inventories/inventory_*.ini
   ```

### Getting Help

1. **Check logs:**
   ```bash
   # Application logs
   make logs-agent
   
   # Terraform logs
   cat artifacts/*/terraform_*.txt
   
   # Ansible logs
   cat artifacts/*/ansible_*.log
   ```

2. **Validate configuration:**
   ```bash
   make validate-terraform
   make validate-ansible
   ```

3. **Debug mode:**
   ```bash
   python pipeline/run.py --env dev --prompt "test" --verbose
   ```

## Next Steps

After successful setup:

1. **Customize playbooks** in `ansible/playbooks/`
2. **Add new Terraform modules** in `terraform/modules/`
3. **Integrate with CI/CD** pipelines
4. **Set up monitoring** and alerting
5. **Implement backup** strategies

## Production Deployment

For production use:

1. **Use remote Terraform state** (S3 + DynamoDB)
2. **Implement proper secret management** (AWS Secrets Manager)
3. **Set up monitoring** and logging
4. **Configure backup** and disaster recovery
5. **Implement security scanning** and compliance checks

See [PRODUCTION.md](PRODUCTION.md) for detailed production deployment guide.
