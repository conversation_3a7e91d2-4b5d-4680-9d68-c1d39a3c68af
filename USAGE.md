# InfraOps Agent Usage Guide

This guide covers how to use the InfraOps AI Automation Agent to provision and configure infrastructure.

## Basic Usage

### Command Line Interface

The main entry point is the `pipeline/run.py` script:

```bash
python pipeline/run.py --env <environment> --prompt "<description>"
```

#### Parameters

- `--env`: Target environment (`dev`, `staging`, `prod`)
- `--prompt`: Natural language description of desired infrastructure
- `--auto-approve`: Skip human approval prompts (use with caution)
- `--verbose`: Enable detailed logging

#### Examples

```bash
# Basic web server
python pipeline/run.py --env dev --prompt "Create a web server with NGINX"

# Development environment
python pipeline/run.py --env dev --prompt "Set up a development server with Dock<PERSON> and Git"

# Production web application
python pipeline/run.py --env prod --prompt "Deploy a production web server with NGINX, Docker, and monitoring"

# Database server
python pipeline/run.py --env staging --prompt "Create a database server with PostgreSQL"
```

### Using Make Commands

For convenience, use the provided Makefile:

```bash
# Quick development deployment
make run-dev

# With auto-approval
make run-dev-auto

# Staging deployment
make run-staging
```

## Natural Language Prompts

The agent understands various natural language patterns:

### Infrastructure Types

```bash
# Web servers
"Create a web server"
"Deploy NGINX server"
"Set up a website"

# Application servers
"Create an application server"
"Deploy a backend API"
"Set up a microservice"

# Database servers
"Create a database server"
"Deploy PostgreSQL"
"Set up a data store"

# Container environments
"Set up Docker environment"
"Create a container cluster"
"Deploy with Kubernetes"
```

### Applications and Services

```bash
# Web servers
"with NGINX"
"with Apache"
"with reverse proxy"

# Containers
"with Docker"
"with Docker Compose"
"with container support"

# Databases
"with PostgreSQL"
"with MySQL"
"with Redis cache"

# Development tools
"with Git"
"with Node.js"
"with Python"

# Monitoring
"with monitoring"
"with logging"
"with metrics"
```

### Instance Sizes

```bash
# Small instances
"small server"
"minimal setup"
"free tier"

# Medium instances
"medium server"
"standard setup"
"staging environment"

# Large instances
"large server"
"high performance"
"production environment"
```

### Regions

```bash
# US regions
"in us-west-2"
"in Oregon"
"in us-east-1"
"in Virginia"

# EU regions
"in eu-west-1"
"in Ireland"
"in Europe"

# Asia regions
"in ap-southeast-1"
"in Singapore"
"in Asia"
```

## Pipeline Workflow

### 1. Prompt Processing

The agent analyzes your natural language prompt and extracts:
- Infrastructure type (web server, database, etc.)
- Required applications (NGINX, Docker, etc.)
- Instance size and region
- Security requirements
- Environment-specific settings

### 2. Terraform Phase

1. **Initialization**: Sets up Terraform working directory
2. **Planning**: Generates execution plan
3. **Approval**: Shows plan and waits for confirmation (unless `--auto-approve`)
4. **Apply**: Provisions infrastructure
5. **Outputs**: Captures server IPs and metadata

### 3. Ansible Phase

1. **Inventory Generation**: Creates dynamic inventory from Terraform outputs
2. **Approval**: Confirms Ansible execution (unless `--auto-approve`)
3. **Bootstrap**: Runs initial server setup
4. **Hardening**: Applies security configurations
5. **Applications**: Installs requested software

### 4. Reporting

1. **Execution Summary**: Generates detailed report
2. **Infrastructure Inventory**: Lists created resources
3. **Access Information**: Provides connection details
4. **Cost Estimation**: Shows estimated monthly costs

## Advanced Usage

### Custom Terraform Variables

Override default variables by editing `terraform/live/<env>/terraform.tfvars`:

```hcl
# Custom instance type
instance_type = "t3.large"

# Custom region
region = "eu-west-1"

# Custom security groups
allowed_ssh_cidrs = ["***********/24"]

# Custom storage
root_volume_size = 50
root_volume_type = "gp3"
```

### Custom Ansible Variables

Create custom variable files in `ansible/`:

```yaml
# ansible/group_vars/all.yml
custom_applications:
  - nodejs
  - mongodb
  - redis

security_hardening: true
backup_enabled: true
```

### Environment-Specific Configurations

#### Development Environment

```bash
# Minimal resources, relaxed security
python pipeline/run.py --env dev --prompt "Create a simple web server for testing"
```

Features:
- t3.micro instances (free tier)
- Basic security settings
- No backup or monitoring
- Public access allowed

#### Staging Environment

```bash
# Production-like but smaller
python pipeline/run.py --env staging --prompt "Create a staging web server with monitoring"
```

Features:
- t3.small instances
- Enhanced security
- Basic monitoring
- Limited access

#### Production Environment

```bash
# Full security and monitoring
python pipeline/run.py --env prod --prompt "Deploy a production web server with high availability"
```

Features:
- t3.large+ instances
- Full security hardening
- Comprehensive monitoring
- Backup enabled
- Restricted access

## Monitoring and Observability

### Accessing Services

After deployment, access your services:

```bash
# SSH to server
ssh -i ~/.ssh/infraops_key ubuntu@<public_ip>

# Check service status
sudo systemctl status nginx docker

# View logs
sudo journalctl -u nginx -f
```

### Web Interfaces

```bash
# Open web server
curl http://<public_ip>

# Health check
curl http://<public_ip>/health
```

### Development Tools

```bash
# Jupyter Lab for experimentation
make jupyter

# Grafana for monitoring
make grafana

# Prometheus for metrics
make prometheus
```

## Managing Infrastructure

### Viewing Current State

```bash
# Check Terraform state
cd terraform/live/dev
terraform show

# List resources
terraform state list

# Get outputs
terraform output
```

### Updating Infrastructure

```bash
# Modify terraform.tfvars
nano terraform/live/dev/terraform.tfvars

# Plan changes
make plan

# Apply changes
make apply
```

### Destroying Infrastructure

```bash
# Destroy specific environment
cd terraform/live/dev
terraform destroy

# Or use make command
make destroy-infra
```

## Troubleshooting

### Common Issues

1. **Permission Denied (SSH)**
   ```bash
   # Fix SSH key permissions
   chmod 600 ~/.ssh/infraops_key
   
   # Check security group rules
   aws ec2 describe-security-groups --group-ids <sg-id>
   ```

2. **Terraform Apply Fails**
   ```bash
   # Check AWS credentials
   aws sts get-caller-identity
   
   # Validate configuration
   terraform validate
   
   # Check for resource conflicts
   terraform plan
   ```

3. **Ansible Connection Fails**
   ```bash
   # Test connectivity
   ansible all -i inventory.ini -m ping
   
   # Check inventory file
   cat ansible/inventories/inventory_*.ini
   
   # Verify SSH access
   ssh -i ~/.ssh/infraops_key ubuntu@<ip>
   ```

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
python pipeline/run.py --env dev --prompt "test" --verbose
```

### Log Files

Check execution logs:

```bash
# Pipeline logs
cat artifacts/<execution_id>/pipeline.log

# Terraform logs
cat artifacts/<execution_id>/terraform_*.txt

# Ansible logs
cat artifacts/<execution_id>/ansible_*.log
```

## Best Practices

### Security

1. **Always specify your IP** in `allowed_ssh_cidrs`
2. **Use strong SSH keys** (4096-bit RSA minimum)
3. **Regularly rotate credentials**
4. **Enable MFA** on AWS account
5. **Review security groups** regularly

### Cost Management

1. **Use appropriate instance sizes** for each environment
2. **Stop/terminate unused instances**
3. **Monitor AWS billing** regularly
4. **Set up cost alerts**
5. **Use spot instances** for development

### Operational

1. **Test in development** before production
2. **Use version control** for configurations
3. **Document custom changes**
4. **Implement backup strategies**
5. **Monitor infrastructure health**

## Integration

### CI/CD Integration

```yaml
# Example GitHub Actions workflow
name: Deploy Infrastructure
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to staging
        run: |
          python pipeline/run.py --env staging --prompt "Deploy staging environment" --auto-approve
```

### API Integration

```python
# Example Python integration
from pipeline.run import InfraOpsOrchestrator

orchestrator = InfraOpsOrchestrator("dev", project_root)
success = orchestrator.run_pipeline(
    prompt="Create a web server with NGINX",
    auto_approve=True
)
```

## Next Steps

1. **Customize playbooks** for your specific needs
2. **Add monitoring** and alerting
3. **Implement backup** strategies
4. **Set up CI/CD** integration
5. **Scale to multiple environments**
