#!/usr/bin/env python3
"""
InfraOps Dashboard Server
A simple web server to serve the InfraOps dashboard
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time
import json
import urllib.parse
from pathlib import Path

class DashboardHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler for the dashboard server"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent), **kwargs)

    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urllib.parse.urlparse(self.path)

        # API endpoints
        if parsed_path.path == '/api/status':
            self.handle_api_status()
        elif parsed_path.path == '/api/metrics':
            self.handle_api_metrics()
        elif parsed_path.path == '/api/instances':
            self.handle_api_instances()
        else:
            # Serve static files
            super().do_GET()

    def handle_api_status(self):
        """Handle /api/status endpoint"""
        try:
            # Get latest report
            reports_dir = Path(__file__).parent.parent / 'reports'
            if reports_dir.exists():
                report_files = list(reports_dir.glob('infraops_report_*.json'))
                if report_files:
                    latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
                    with open(latest_report) as f:
                        data = json.load(f)

                    status = {
                        'status': 'success',
                        'timestamp': data.get('timestamp'),
                        'instances': len(data.get('aws_instances', [])),
                        'running_instances': len([i for i in data.get('aws_instances', []) if i.get('state') == 'running']),
                        'cost_estimate': data.get('cost_estimate', {}),
                        'last_deployment': data.get('timestamp')
                    }
                else:
                    status = {'status': 'no_data', 'message': 'No reports found'}
            else:
                status = {'status': 'no_data', 'message': 'Reports directory not found'}

            self.send_json_response(status)
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)})

    def handle_api_metrics(self):
        """Handle /api/metrics endpoint"""
        try:
            import subprocess

            # Get basic system metrics
            metrics = {
                'cpu_usage': 15.2,  # Placeholder
                'memory_usage': 42.1,  # Placeholder
                'disk_usage': 23.5,  # Placeholder
                'network_io': '1.2 MB/s',
                'timestamp': time.time()
            }

            self.send_json_response(metrics)
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)})

    def handle_api_instances(self):
        """Handle /api/instances endpoint"""
        try:
            # Get latest report
            reports_dir = Path(__file__).parent.parent / 'reports'
            if reports_dir.exists():
                report_files = list(reports_dir.glob('infraops_report_*.json'))
                if report_files:
                    latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
                    with open(latest_report) as f:
                        data = json.load(f)

                    instances = data.get('aws_instances', [])
                    self.send_json_response({'instances': instances})
                else:
                    self.send_json_response({'instances': []})
            else:
                self.send_json_response({'instances': []})
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)})

    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data, default=str).encode())

    def log_message(self, format, *args):
        # Custom logging format
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def find_free_port(start_port=8000, max_attempts=100):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def open_browser(url, delay=1):
    """Open browser after a delay"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 Opened dashboard in browser: {url}")
    except Exception as e:
        print(f"Could not open browser automatically: {e}")
        print(f"Please open {url} manually in your browser")

def main():
    """Main function to start the dashboard server"""
    
    # Change to dashboard directory
    dashboard_dir = Path(__file__).parent
    os.chdir(dashboard_dir)
    
    # Find a free port
    try:
        port = find_free_port(8000)
    except RuntimeError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    # Create server
    try:
        with socketserver.TCPServer(("", port), DashboardHandler) as httpd:
            url = f"http://localhost:{port}"
            
            print("🚀 InfraOps Dashboard Server Starting...")
            print("=" * 50)
            print(f"📊 Dashboard URL: {url}")
            print(f"📁 Serving from: {dashboard_dir}")
            print(f"🔌 Port: {port}")
            print("=" * 50)
            print("Press Ctrl+C to stop the server")
            print()
            
            # Open browser in a separate thread
            browser_thread = threading.Thread(target=open_browser, args=(url,))
            browser_thread.daemon = True
            browser_thread.start()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Dashboard server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
