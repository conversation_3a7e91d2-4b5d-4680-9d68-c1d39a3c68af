<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>noOps Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-running { background-color: #48bb78; }
        .status-stopped { background-color: #f56565; }
        .status-pending { background-color: #ed8936; }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #4a5568;
        }

        .metric-value {
            font-weight: 600;
            color: #2d3748;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .infrastructure-list {
            list-style: none;
        }

        .infrastructure-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .infrastructure-item strong {
            color: #2d3748;
        }

        .infrastructure-item small {
            color: #718096;
        }

        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .logs-container {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 noOps Dashboard</h1>
            <p>AI-Powered Infrastructure Management</p>
        </div>

        <div class="dashboard-grid">
            <!-- Infrastructure Status -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-running"></span>
                    Infrastructure Status
                </h3>
                <div class="metric">
                    <span class="metric-label">Active Servers</span>
                    <span class="metric-value" id="active-servers">1</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Cost (Monthly)</span>
                    <span class="metric-value" id="monthly-cost">~$8-10</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Deployment</span>
                    <span class="metric-value" id="last-deployment">Today</span>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-success" onclick="deployInfrastructure()">Deploy New</button>
                    <button class="btn btn-danger" onclick="destroyInfrastructure()">Destroy All</button>
                </div>
            </div>

            <!-- Current Infrastructure -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-running"></span>
                    Current Infrastructure
                </h3>
                <ul class="infrastructure-list" id="infrastructure-list">
                    <li class="infrastructure-item">
                        <div>
                            <strong>noops-dev-server</strong><br>
                            <small>t3.micro • us-west-2 • ***********</small>
                        </div>
                        <span class="status-indicator status-running"></span>
                    </li>
                </ul>
                <div class="quick-actions">
                    <a href="http://***********" target="_blank" class="btn btn-secondary">View Server</a>
                    <button class="btn" onclick="refreshInfrastructure()">Refresh</button>
                </div>
            </div>

            <!-- Recent Deployments -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-running"></span>
                    Recent Deployments
                </h3>
                <div class="metric">
                    <span class="metric-label">Web Server + Docker</span>
                    <span class="metric-value">✅ Success</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Security Hardening</span>
                    <span class="metric-value">✅ Applied</span>
                </div>
                <div class="metric">
                    <span class="metric-label">NGINX + Applications</span>
                    <span class="metric-value">✅ Running</span>
                </div>
                <div class="quick-actions">
                    <button class="btn" onclick="viewLogs()">View Logs</button>
                    <button class="btn btn-secondary" onclick="downloadReport()">Download Report</button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-pending"></span>
                    Quick Actions
                </h3>
                <div class="quick-actions">
                    <button class="btn" onclick="promptDeploy()">🤖 AI Deploy</button>
                    <button class="btn btn-secondary" onclick="openSSH()">🔧 SSH Access</button>
                    <button class="btn btn-secondary" onclick="viewMonitoring()">📊 Monitoring</button>
                    <button class="btn" onclick="backupData()">💾 Backup</button>
                </div>
                <div style="margin-top: 15px;">
                    <input type="text" id="ai-prompt" placeholder="Describe your infrastructure needs..." 
                           style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 6px; margin-bottom: 10px;">
                    <button class="btn btn-success" onclick="executeAIPrompt()" style="width: 100%;">
                        Execute AI Command
                    </button>
                </div>
            </div>

            <!-- System Metrics -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-running"></span>
                    System Metrics
                </h3>
                <div class="metric">
                    <span class="metric-label">CPU Usage</span>
                    <span class="metric-value">12%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage</span>
                    <span class="metric-value">45%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Disk Usage</span>
                    <span class="metric-value">23%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Network I/O</span>
                    <span class="metric-value">1.2 MB/s</span>
                </div>
            </div>

            <!-- Logs -->
            <div class="card">
                <h3>
                    <span class="status-indicator status-running"></span>
                    Recent Logs
                </h3>
                <div class="logs-container" id="logs-container">
                    <div>2024-08-21 16:45:23 [INFO] Pipeline execution completed successfully</div>
                    <div>2024-08-21 16:45:20 [INFO] Ansible playbook app.yml completed</div>
                    <div>2024-08-21 16:44:15 [INFO] Security hardening applied</div>
                    <div>2024-08-21 16:43:10 [INFO] EC2 instance i-044318410f4e56ffa created</div>
                    <div>2024-08-21 16:42:05 [INFO] Terraform apply started</div>
                </div>
                <div class="quick-actions">
                    <button class="btn" onclick="refreshLogs()">Refresh Logs</button>
                    <button class="btn btn-secondary" onclick="downloadLogs()">Download</button>
                </div>
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshDashboard()" title="Refresh Dashboard">
        🔄
    </button>

    <script>
        // Dashboard JavaScript functionality
        function refreshDashboard() {
            console.log('Refreshing dashboard...');
            // Add refresh animation
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
            }, 500);
            
            // Simulate data refresh
            updateMetrics();
        }

        function updateMetrics() {
            // Simulate real-time metrics updates
            const cpuUsage = Math.floor(Math.random() * 30) + 10;
            const memUsage = Math.floor(Math.random() * 40) + 30;
            const diskUsage = Math.floor(Math.random() * 20) + 15;
            
            // Update if elements exist
            const metrics = document.querySelectorAll('.metric-value');
            if (metrics.length >= 4) {
                metrics[metrics.length - 4].textContent = cpuUsage + '%';
                metrics[metrics.length - 3].textContent = memUsage + '%';
                metrics[metrics.length - 2].textContent = diskUsage + '%';
            }
        }

        function deployInfrastructure() {
            if (confirm('Deploy new infrastructure? This will create AWS resources.')) {
                alert('Deployment started! Check the logs for progress.');
                addLog('Starting new infrastructure deployment...');
            }
        }

        function destroyInfrastructure() {
            if (confirm('⚠️ WARNING: This will destroy ALL infrastructure. Are you sure?')) {
                alert('Infrastructure destruction initiated. This may take a few minutes.');
                addLog('Infrastructure destruction started...');
            }
        }

        function promptDeploy() {
            const prompt = document.getElementById('ai-prompt').value;
            if (prompt) {
                executeAIPrompt();
            } else {
                document.getElementById('ai-prompt').focus();
                document.getElementById('ai-prompt').placeholder = 'Enter your infrastructure request here...';
            }
        }

        function executeAIPrompt() {
            const prompt = document.getElementById('ai-prompt').value;
            if (!prompt) {
                alert('Please enter a description of your infrastructure needs.');
                return;
            }
            
            alert(`AI Processing: "${prompt}"\n\nThis would execute the noOps pipeline with your request.`);
            addLog(`AI Request: ${prompt}`);
            document.getElementById('ai-prompt').value = '';
        }

        function openSSH() {
            const sshCommand = 'ssh -i ~/.ssh/noops_key ubuntu@***********';
            if (navigator.clipboard) {
                navigator.clipboard.writeText(sshCommand);
                alert('SSH command copied to clipboard:\n' + sshCommand);
            } else {
                alert('SSH Command:\n' + sshCommand);
            }
        }

        function viewMonitoring() {
            window.open('http://localhost:3000', '_blank'); // Grafana
        }

        function backupData() {
            alert('Backup initiated. This will create snapshots of all EBS volumes.');
            addLog('Backup process started...');
        }

        function viewLogs() {
            const logsContainer = document.getElementById('logs-container');
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function refreshLogs() {
            addLog('Dashboard refreshed at ' + new Date().toLocaleTimeString());
        }

        function downloadLogs() {
            alert('Logs download started. Check your downloads folder.');
        }

        function downloadReport() {
            alert('Execution report download started.');
        }

        function refreshInfrastructure() {
            addLog('Infrastructure status refreshed');
            updateMetrics();
        }

        function addLog(message) {
            const logsContainer = document.getElementById('logs-container');
            const timestamp = new Date().toLocaleString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `${timestamp} [INFO] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // Auto-refresh metrics every 30 seconds
        setInterval(updateMetrics, 30000);

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('noOps Dashboard loaded');
            addLog('Dashboard initialized');
        });
    </script>
</body>
</html>
