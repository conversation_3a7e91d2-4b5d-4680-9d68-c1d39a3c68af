version: '3.8'

services:
  # InfraOps Agent - Main application container
  infraops-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: infraops-agent
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - LOG_LEVEL=INFO
    volumes:
      # Mount source code for development
      - .:/app
      # Mount AWS credentials (if available)
      - ~/.aws:/root/.aws:ro
      # Mount SSH keys
      - ~/.ssh:/root/.ssh:ro
      # Mount artifacts directory
      - ./artifacts:/app/artifacts
      # Mount Terraform state (for local development)
      - ./terraform:/app/terraform
    working_dir: /app
    command: tail -f /dev/null  # Keep container running for development
    networks:
      - infraops-network
    ports:
      - "8080:8080"  # For future web UI
    depends_on:
      - redis
      - postgres

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: infraops-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - infraops-network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for storing execution history and metadata
  postgres:
    image: postgres:15-alpine
    container_name: infraops-postgres
    environment:
      POSTGRES_DB: infraops
      POSTGRES_USER: infraops
      POSTGRES_PASSWORD: infraops_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - infraops-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U infraops"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jupyter Notebook for development and experimentation
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: infraops-jupyter
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=infraops-dev-token
    ports:
      - "8888:8888"
    volumes:
      - .:/home/<USER>/work
      - jupyter_data:/home/<USER>/.jupyter
    networks:
      - infraops-network
    command: start-notebook.sh --NotebookApp.token='infraops-dev-token' --NotebookApp.password=''

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: infraops-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - infraops-network
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: infraops-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=infraops-admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - infraops-network
    profiles:
      - monitoring
    depends_on:
      - prometheus

  # LocalStack for AWS service emulation (development/testing)
  localstack:
    image: localstack/localstack:latest
    container_name: infraops-localstack
    ports:
      - "4566:4566"  # LocalStack main port
      - "4571:4571"  # LocalStack dashboard
    environment:
      - SERVICES=ec2,s3,iam,cloudformation
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - localstack_data:/tmp/localstack
    networks:
      - infraops-network
    profiles:
      - testing

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  jupyter_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  localstack_data:
    driver: local

networks:
  infraops-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
