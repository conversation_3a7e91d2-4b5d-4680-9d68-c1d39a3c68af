# InfraOps AI Automation Agent

A local-first DevOps automation project that takes natural language prompts and automatically provisions and configures cloud infrastructure using Terraform and Ansible.

## 🚀 Features

- **Natural Language Processing**: Describe your infrastructure needs in plain English
- **Terraform Integration**: Automatic generation and execution of Terraform code
- **Ansible Automation**: Dynamic inventory generation and playbook execution
- **Security First**: Human approval checkpoints and secure secret management
- **Local Development**: Docker Compose setup for easy local testing
- **Comprehensive Reporting**: Detailed reports of all provisioned resources

## 📁 Project Structure

```
infraops/
├── agent/                    # AI prompt templates and helper scripts
│   ├── prompts/             # Natural language prompt templates
│   ├── parsers/             # AI response parsing utilities
│   └── templates/           # Infrastructure code templates
├── terraform/
│   ├── modules/             # Reusable Terraform modules
│   │   └── ec2_basic/       # Basic EC2 instance module
│   └── live/                # Environment-specific configurations
│       └── dev/             # Development environment
├── ansible/
│   ├── playbooks/           # Ansible playbooks
│   ├── roles/               # Reusable Ansible roles
│   └── inventories/         # Dynamic inventory files
├── pipeline/
│   ├── run.py              # Main orchestrator script
│   ├── terraform_runner.py # Terraform execution wrapper
│   └── ansible_runner.py   # Ansible execution wrapper
├── artifacts/               # Generated reports and logs
├── docker-compose.yml       # Local development environment
└── requirements.txt         # Python dependencies
```

## 🛠️ Prerequisites

- Python 3.8+
- Terraform >= 1.0
- Ansible >= 4.0
- Docker & Docker Compose
- AWS CLI configured with appropriate credentials
- SSH key pair for server access

## 🚀 Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd infraops
   pip install -r requirements.txt
   ```

2. **Configure AWS Credentials**
   ```bash
   aws configure
   ```

3. **Generate SSH Key (if needed)**
   ```bash
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/infraops_key
   ```

4. **Customize Configuration**
   - Edit `terraform/live/dev/terraform.tfvars`
   - Update SSH key path and allowed CIDR blocks

5. **Run the Pipeline**
   ```bash
   python pipeline/run.py --env dev --prompt "Create a web server with NGINX and Docker"
   ```

## 📋 Usage Examples

### Basic Web Server
```bash
python pipeline/run.py --env dev --prompt "Deploy a Ubuntu server with NGINX and Docker in us-west-2"
```

### Development Environment
```bash
python pipeline/run.py --env dev --prompt "Create a development server with Docker, Git, and Node.js"
```

## 🔧 Configuration

### Terraform Variables
Edit `terraform/live/dev/terraform.tfvars`:
```hcl
region = "us-west-2"
instance_type = "t3.micro"
ssh_public_key_path = "~/.ssh/infraops_key.pub"
allowed_ssh_cidrs = ["YOUR_IP/32"]
```

### Ansible Configuration
Customize playbooks in `ansible/playbooks/` for your specific needs.

## 🔒 Security Features

- Human approval required before infrastructure changes
- SSH access restricted to specified CIDR blocks
- Ansible Vault for sensitive data
- No hardcoded secrets in code
- UFW firewall configuration
- SSH hardening by default

## 📊 Reporting

After each run, check the `artifacts/` directory for:
- Terraform execution logs
- Ansible playbook results
- Infrastructure summary reports
- Resource inventory

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### Common Issues

1. **Terraform Authentication**: Ensure AWS credentials are properly configured
2. **SSH Access**: Verify your IP is in the allowed CIDR blocks
3. **Ansible Connection**: Check that the SSH key has proper permissions (600)

### Getting Help

- Check the `artifacts/` directory for detailed logs
- Review Terraform and Ansible documentation
- Open an issue on GitHub

## 🔄 Roadmap

- [ ] Support for multiple cloud providers (Azure, GCP)
- [ ] Advanced AI prompt processing
- [ ] Web UI for easier interaction
- [ ] Integration with monitoring tools
- [ ] Kubernetes deployment support
