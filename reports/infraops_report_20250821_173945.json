{"timestamp": "2025-08-21T17:39:41.586230", "aws_instances": [{"id": "i-044318410f4e56ffa", "type": "t3.micro", "state": "running", "public_ip": "***********", "private_ip": "*************", "launch_time": "2025-08-21T11:22:10+00:00", "tags": {"Name": "infraops-dev-server", "ManagedBy": "terraform", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "CreatedBy": "infraops-agent", "Environment": "dev", "Project": "infraops"}}], "terraform_state": {"format_version": "1.0"}, "ansible_status": {"execution_id": "20250821_164124", "timestamp": "2025-08-21T16:41:24.829997", "playbooks": []}, "system_metrics": {"cpu_usage": 0, "memory_usage": 40.236648131384975, "disk_usage": 22, "timestamp": "2025-08-21T17:39:44.023294"}, "cost_estimate": {"hourly": 0.0104, "daily": 0.25, "monthly": 7.49, "currency": "USD"}}