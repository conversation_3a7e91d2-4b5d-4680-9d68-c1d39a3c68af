# Prometheus configuration for InfraOps monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # InfraOps Agent (when running)
  - job_name: 'infraops-agent'
    static_configs:
      - targets: ['infraops-agent:8080']
    scrape_interval: 30s
    metrics_path: '/metrics'

  # Node Exporter (for system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker metrics (if available)
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-host:9323']
    scrape_interval: 30s

  # AWS EC2 instances (when deployed)
  - job_name: 'aws-instances'
    static_configs:
      - targets: []  # Will be populated dynamically
    scrape_interval: 60s

  # Application metrics
  - job_name: 'applications'
    static_configs:
      - targets: []  # Will be populated based on deployed apps
    scrape_interval: 30s
