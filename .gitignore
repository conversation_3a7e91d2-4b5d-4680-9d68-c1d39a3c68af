# InfraOps Agent .gitignore

# Environment and configuration files
.env
.env.local
.env.production
.env.staging

# Terraform files
*.tfstate
*.tfstate.*
*.tfvars
!*.tfvars.example
.terraform/
.terraform.lock.hcl
terraform.tfplan
terraform.tfplan.*

# Ansible files
ansible/inventories/inventory_*.ini
!ansible/inventories/inventory.ini.example
ansible/group_vars/all/vault.yml
ansible/host_vars/*/vault.yml
*.retry

# SSH keys and certificates
*.pem
*.key
*.crt
*.p12
id_rsa*
infraops_key*
!*.pub.example

# AWS credentials
.aws/
credentials
config

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs and artifacts
logs/
artifacts/
*.log
*.out
*.err

# Docker
.dockerignore

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Backup files
*.backup
*.bak
*.orig
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Monitoring and metrics
prometheus_data/
grafana_data/

# Local development
local/
.local/

# Secrets and sensitive data
secrets/
vault/
*.secret
*.password

# Generated documentation
docs/_build/
site/

# Package files
*.tar.gz
*.zip
*.rar

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ruby (if using any Ruby tools)
*.gem
.bundle/
vendor/bundle/

# Go (if using any Go tools)
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# Rust (if using any Rust tools)
target/
Cargo.lock

# Java (if using any Java tools)
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Terraform provider cache
.terraform.d/

# Ansible Galaxy
ansible/roles/*/
!ansible/roles/common/
!ansible/roles/docker/
!ansible/roles/nginx/

# Local configuration overrides
local.yml
local.yaml
local.json
override.yml
override.yaml
override.json

# Test results
test-results/
coverage.xml
*.cover
.hypothesis/

# Profiling data
.prof

# Kubernetes
*.kubeconfig
kubeconfig*

# Helm
charts/*.tgz

# Vagrant
.vagrant/

# Packer
packer_cache/

# Consul
.consul/

# Vault
.vault/

# Nomad
.nomad/

# Local state
.state/
state.json

# Cache directories
.cache/
cache/

# Lock files
*.lock
!requirements.txt.lock

# Compiled binaries
bin/
!bin/.gitkeep

# Generated files
generated/
auto-generated/

# Backup directories
backup/
backups/

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Video files
*.mp4
*.avi
*.mov
*.wmv

# Audio files
*.mp3
*.wav
*.flac

# Image files (except small icons/logos)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.ico
!logo.png
!icon.png

# Large data files
*.csv
*.json
*.xml
!small-*.json
!config.json
!package.json

# Compiled documentation
*.pdf
*.doc
*.docx
!README.pdf

# Temporary editor files
.#*
\#*#
*~

# Emacs
.dir-locals.el

# Vim
.vim/
*.vim

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Visual Studio Code
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix
