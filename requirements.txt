# Core dependencies
click>=8.0.0
pyyaml>=6.0
jinja2>=3.0.0
requests>=2.28.0
python-dotenv>=0.19.0

# Infrastructure automation
ansible>=6.0.0
ansible-core>=2.13.0

# AWS integration
boto3>=1.26.0
botocore>=1.29.0

# Logging and monitoring
colorlog>=6.7.0
rich>=12.0.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0

# Optional: AI/ML libraries for future enhancements
# openai>=0.27.0
# langchain>=0.0.200
