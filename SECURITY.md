# Security Guide for InfraOps Agent

This document outlines security best practices and configurations for the InfraOps AI Automation Agent.

## Security Architecture

### Defense in Depth

The InfraOps Agent implements multiple layers of security:

1. **Infrastructure Security**: AWS security groups, VPCs, encryption
2. **Application Security**: SSH hardening, firewall rules, fail2ban
3. **Access Control**: SSH keys, IP restrictions, user management
4. **Data Protection**: Encryption at rest and in transit
5. **Monitoring**: Logging, alerting, audit trails

## Pre-Deployment Security

### 1. AWS Account Security

```bash
# Enable MFA on root account
aws iam enable-mfa-device --user-name root --serial-number <device-arn> --authentication-code1 <code1> --authentication-code2 <code2>

# Create IAM user with minimal permissions
aws iam create-user --user-name infraops-agent
aws iam attach-user-policy --user-name infraops-agent --policy-arn arn:aws:iam::aws:policy/AmazonEC2FullAccess
```

### 2. SSH Key Management

```bash
# Generate strong SSH keys
ssh-keygen -t rsa -b 4096 -f ~/.ssh/infraops_key -C "infraops-agent-$(date +%Y%m%d)"

# Set proper permissions
chmod 600 ~/.ssh/infraops_key
chmod 644 ~/.ssh/infraops_key.pub

# Backup keys securely
cp ~/.ssh/infraops_key* /secure/backup/location/
```

### 3. Network Security Configuration

Edit `terraform/live/dev/terraform.tfvars`:

```hcl
# CRITICAL: Replace with your actual IP address
allowed_ssh_cidrs = ["*************/32"]  # Your IP only

# Restrict HTTP/HTTPS if needed
allowed_http_cidrs = ["0.0.0.0/0"]   # Public web access
allowed_https_cidrs = ["0.0.0.0/0"]  # Public HTTPS access

# For internal applications, restrict further:
# allowed_http_cidrs = ["10.0.0.0/8", "**********/12", "***********/16"]
```

## Infrastructure Security

### 1. EC2 Security

The Terraform module implements several security measures:

```hcl
# Encrypted EBS volumes
root_block_device {
  encrypted = true
  volume_type = "gp3"
}

# Security groups with minimal access
resource "aws_security_group" "infraops" {
  # SSH only from specified IPs
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_ssh_cidrs
  }
}

# Instance metadata service v2 (IMDSv2)
metadata_options {
  http_endpoint = "enabled"
  http_tokens   = "required"
  http_put_response_hop_limit = 1
}
```

### 2. Network Security

```bash
# VPC Configuration (for production)
# Consider using private subnets with NAT Gateway
vpc_id = "vpc-12345678"
subnet_id = "subnet-87654321"  # Private subnet

# Security group rules
# Principle of least privilege - only open required ports
```

## Application Security

### 1. SSH Hardening

The `harden.yml` playbook implements SSH security:

```yaml
# Disable root login
PermitRootLogin no

# Key-only authentication
PasswordAuthentication no
PubkeyAuthentication yes

# Connection limits
MaxAuthTries 3
MaxSessions 2
ClientAliveInterval 300
ClientAliveCountMax 2

# Protocol and cipher restrictions
Protocol 2
Ciphers aes256-ctr,aes192-ctr,aes128-ctr
MACs hmac-sha2-256,hmac-sha2-512
```

### 2. Firewall Configuration

```yaml
# UFW firewall rules
- name: Configure UFW defaults
  ufw:
    direction: incoming
    policy: deny

- name: Allow SSH
  ufw:
    rule: allow
    port: 22
    proto: tcp

- name: Allow HTTP/HTTPS
  ufw:
    rule: allow
    port: "{{ item }}"
    proto: tcp
  loop: [80, 443]
```

### 3. Intrusion Detection

```yaml
# Fail2ban configuration
[sshd]
enabled = true
port = 22
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
findtime = 600
```

## Secret Management

### 1. Environment Variables

Never commit secrets to version control:

```bash
# Use .env file (not committed)
AWS_ACCESS_KEY_ID=your_key_here
AWS_SECRET_ACCESS_KEY=your_secret_here

# Or use AWS IAM roles (recommended)
```

### 2. Ansible Vault

For sensitive Ansible variables:

```bash
# Create encrypted file
ansible-vault create ansible/group_vars/all/vault.yml

# Edit encrypted file
ansible-vault edit ansible/group_vars/all/vault.yml

# Example vault content:
vault_database_password: "super_secure_password"
vault_api_key: "secret_api_key"
```

### 3. AWS Secrets Manager

For production environments:

```python
import boto3

def get_secret(secret_name, region_name="us-west-2"):
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )
    return client.get_secret_value(SecretId=secret_name)
```

## Access Control

### 1. User Management

The bootstrap playbook creates a secure deployment user:

```yaml
- name: Create deploy user
  user:
    name: deploy
    shell: /bin/bash
    create_home: yes
    groups: sudo
    append: yes

- name: Set up sudo access
  lineinfile:
    path: /etc/sudoers.d/deploy
    line: "deploy ALL=(ALL) NOPASSWD:ALL"
    create: yes
    mode: '0440'
```

### 2. SSH Key Distribution

```yaml
- name: Copy authorized_keys to deploy user
  copy:
    src: /home/<USER>/.ssh/authorized_keys
    dest: /home/<USER>/.ssh/authorized_keys
    owner: deploy
    group: deploy
    mode: '0600'
    remote_src: yes
```

## Monitoring and Auditing

### 1. System Monitoring

```yaml
# Install monitoring tools
- name: Install monitoring packages
  apt:
    name:
      - auditd
      - aide
      - rkhunter
      - chkrootkit
    state: present
```

### 2. Log Management

```yaml
# Configure log rotation
- name: Configure logrotate for security logs
  copy:
    content: |
      /var/log/auth.log {
          weekly
          missingok
          rotate 52
          compress
          delaycompress
          notifempty
          create 640 root adm
      }
    dest: /etc/logrotate.d/auth
```

### 3. AWS CloudTrail

Enable CloudTrail for API auditing:

```bash
aws cloudtrail create-trail --name infraops-audit-trail --s3-bucket-name infraops-audit-logs
aws cloudtrail start-logging --name infraops-audit-trail
```

## Security Validation

### 1. Automated Security Checks

```bash
# Run security validation
make check-secrets

# Validate Terraform security
terraform plan | grep -i security

# Check for open security groups
aws ec2 describe-security-groups --query 'SecurityGroups[?IpPermissions[?IpRanges[?CidrIp==`0.0.0.0/0`]]]'
```

### 2. Manual Security Review

```bash
# SSH to server and check security
ssh -i ~/.ssh/infraops_key ubuntu@<server_ip>

# Check running services
sudo netstat -tlnp

# Check firewall status
sudo ufw status verbose

# Check fail2ban status
sudo fail2ban-client status

# Check for unauthorized users
cat /etc/passwd | grep -v nologin

# Check sudo access
sudo cat /etc/sudoers.d/*
```

### 3. Security Scanning

```bash
# Run security scan with Lynis
sudo lynis audit system

# Check for rootkits
sudo rkhunter --check

# File integrity check
sudo aide --check
```

## Incident Response

### 1. Security Incident Detection

Monitor for:
- Failed SSH attempts
- Unusual network traffic
- Unauthorized file changes
- Privilege escalation attempts

### 2. Response Procedures

```bash
# Immediate response
# 1. Isolate affected systems
aws ec2 modify-instance-attribute --instance-id i-1234567890abcdef0 --groups sg-emergency

# 2. Preserve evidence
sudo dd if=/dev/xvda of=/tmp/disk-image.dd

# 3. Analyze logs
sudo grep "Failed password" /var/log/auth.log
sudo journalctl -u ssh --since "1 hour ago"

# 4. Block malicious IPs
sudo fail2ban-client set sshd banip <malicious_ip>
```

## Compliance and Best Practices

### 1. Security Frameworks

The InfraOps Agent aligns with:
- **CIS Controls**: Critical Security Controls
- **NIST Cybersecurity Framework**: Identify, Protect, Detect, Respond, Recover
- **AWS Well-Architected Security Pillar**: Security best practices

### 2. Regular Security Tasks

```bash
# Weekly security tasks
# 1. Update systems
sudo apt update && sudo apt upgrade

# 2. Review logs
sudo grep -i "error\|fail\|warn" /var/log/syslog

# 3. Check for unauthorized changes
sudo aide --check

# 4. Review user accounts
sudo cat /etc/passwd | grep -v nologin

# 5. Update security tools
sudo rkhunter --update
sudo freshclam  # if ClamAV is installed
```

### 3. Security Metrics

Track security metrics:
- Failed login attempts
- Security group changes
- Patch compliance
- Certificate expiration
- Vulnerability scan results

## Production Security Checklist

Before deploying to production:

- [ ] AWS MFA enabled
- [ ] IAM roles with minimal permissions
- [ ] SSH keys rotated and secured
- [ ] Security groups restricted to specific IPs
- [ ] EBS encryption enabled
- [ ] VPC with private subnets
- [ ] CloudTrail logging enabled
- [ ] Security monitoring configured
- [ ] Backup and disaster recovery tested
- [ ] Incident response plan documented
- [ ] Security scanning automated
- [ ] Compliance requirements met

## Security Updates

### 1. Automated Updates

```yaml
# Configure automatic security updates
- name: Enable automatic security updates
  lineinfile:
    path: /etc/apt/apt.conf.d/20auto-upgrades
    line: "{{ item }}"
    create: yes
  loop:
    - 'APT::Periodic::Update-Package-Lists "1";'
    - 'APT::Periodic::Unattended-Upgrade "1";'
```

### 2. Manual Update Process

```bash
# Regular update schedule
# 1. Test updates in development
make run-dev

# 2. Apply to staging
make run-staging

# 3. Deploy to production (with approval)
python pipeline/run.py --env prod --prompt "Apply security updates"
```

## Contact and Reporting

For security issues:
1. **Do not** create public GitHub issues
2. Email security concerns to: <EMAIL>
3. Use encrypted communication when possible
4. Follow responsible disclosure practices

Remember: Security is an ongoing process, not a one-time setup. Regularly review and update your security posture.
