# ansible/inventories/inventory.ini.example
# Example Ansible inventory file - this will be generated dynamically by the pipeline

[web_servers]
# This section will be populated automatically by the pipeline
# Example format:
# infraops-dev-server ansible_host=******* ansible_user=ubuntu ansible_ssh_private_key_file=~/.ssh/infraops_key instance_id=i-1234567890abcdef0 private_ip=**********

[web_servers:vars]
# Common variables for web servers
ansible_python_interpreter=/usr/bin/python3
ansible_ssh_common_args='-o StrictHostKeyChecking=no'

[all:vars]
# Global variables
ansible_user=ubuntu
ansible_ssh_private_key_file=~/.ssh/infraops_key
ansible_host_key_checking=False
ansible_ssh_retries=3
ansible_ssh_timeout=30
