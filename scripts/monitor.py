#!/usr/bin/env python3
"""
InfraOps Monitoring Script
Collects real-time infrastructure metrics and status
"""

import json
import subprocess
import time
import boto3
import os
from datetime import datetime
from pathlib import Path

class InfraOpsMonitor:
    """Monitor for InfraOps infrastructure"""
    
    def __init__(self):
        self.aws_region = os.getenv('AWS_DEFAULT_REGION', 'us-west-2')
        self.project_root = Path(__file__).parent.parent
        
    def get_aws_instances(self):
        """Get AWS EC2 instances"""
        try:
            ec2 = boto3.client('ec2', region_name=self.aws_region)
            response = ec2.describe_instances(
                Filters=[
                    {'Name': 'tag:Project', 'Values': ['infraops']},
                    {'Name': 'instance-state-name', 'Values': ['running', 'pending', 'stopping', 'stopped']}
                ]
            )
            
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append({
                        'id': instance['InstanceId'],
                        'type': instance['InstanceType'],
                        'state': instance['State']['Name'],
                        'public_ip': instance.get('PublicIpAddress', 'N/A'),
                        'private_ip': instance.get('PrivateIpAddress', 'N/A'),
                        'launch_time': instance['LaunchTime'].isoformat() if 'LaunchTime' in instance else None,
                        'tags': {tag['Key']: tag['Value'] for tag in instance.get('Tags', [])}
                    })
            
            return instances
        except Exception as e:
            print(f"Error getting AWS instances: {e}")
            return []
    
    def get_terraform_state(self):
        """Get Terraform state information"""
        try:
            terraform_dir = self.project_root / 'terraform'
            if not terraform_dir.exists():
                return {}
            
            result = subprocess.run(
                ['terraform', 'show', '-json'],
                cwd=terraform_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return {}
        except Exception as e:
            print(f"Error getting Terraform state: {e}")
            return {}
    
    def get_ansible_status(self):
        """Get Ansible execution status"""
        try:
            artifacts_dir = self.project_root / 'artifacts'
            if not artifacts_dir.exists():
                return {}
            
            # Find the most recent execution
            execution_dirs = [d for d in artifacts_dir.iterdir() if d.is_dir()]
            if not execution_dirs:
                return {}
            
            latest_execution = max(execution_dirs, key=lambda x: x.stat().st_mtime)
            
            status = {
                'execution_id': latest_execution.name,
                'timestamp': datetime.fromtimestamp(latest_execution.stat().st_mtime).isoformat(),
                'playbooks': []
            }
            
            # Check for playbook results
            for playbook_file in latest_execution.glob('*.json'):
                try:
                    with open(playbook_file) as f:
                        playbook_data = json.load(f)
                        status['playbooks'].append({
                            'name': playbook_file.stem,
                            'status': 'success' if playbook_data.get('stats', {}).get('failures', 0) == 0 else 'failed',
                            'tasks': playbook_data.get('stats', {})
                        })
                except Exception:
                    continue
            
            return status
        except Exception as e:
            print(f"Error getting Ansible status: {e}")
            return {}
    
    def get_system_metrics(self, host=None):
        """Get system metrics from a host"""
        if not host:
            # Get metrics from local system
            try:
                # CPU usage
                cpu_result = subprocess.run(['top', '-bn1'], capture_output=True, text=True)
                cpu_line = [line for line in cpu_result.stdout.split('\n') if 'Cpu(s)' in line]
                cpu_usage = 0
                if cpu_line:
                    # Parse CPU usage from top output
                    import re
                    match = re.search(r'(\d+\.\d+)%us', cpu_line[0])
                    if match:
                        cpu_usage = float(match.group(1))
                
                # Memory usage
                mem_result = subprocess.run(['free', '-m'], capture_output=True, text=True)
                mem_lines = mem_result.stdout.split('\n')
                if len(mem_lines) > 1:
                    mem_parts = mem_lines[1].split()
                    if len(mem_parts) >= 3:
                        total_mem = int(mem_parts[1])
                        used_mem = int(mem_parts[2])
                        mem_usage = (used_mem / total_mem) * 100
                    else:
                        mem_usage = 0
                else:
                    mem_usage = 0
                
                # Disk usage
                disk_result = subprocess.run(['df', '-h', '/'], capture_output=True, text=True)
                disk_lines = disk_result.stdout.split('\n')
                disk_usage = 0
                if len(disk_lines) > 1:
                    disk_parts = disk_lines[1].split()
                    if len(disk_parts) >= 5:
                        disk_usage = int(disk_parts[4].replace('%', ''))
                
                return {
                    'cpu_usage': cpu_usage,
                    'memory_usage': mem_usage,
                    'disk_usage': disk_usage,
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                print(f"Error getting system metrics: {e}")
                return {}
        else:
            # Get metrics from remote host via SSH
            # This would require SSH access to be configured
            return {}
    
    def get_cost_estimate(self):
        """Get AWS cost estimate"""
        try:
            # This is a simplified cost calculation
            # In production, you'd use AWS Cost Explorer API
            instances = self.get_aws_instances()
            
            # Rough cost estimates per hour (USD)
            instance_costs = {
                't3.micro': 0.0104,
                't3.small': 0.0208,
                't3.medium': 0.0416,
                't3.large': 0.0832,
                't3.xlarge': 0.1664
            }
            
            total_hourly = 0
            for instance in instances:
                if instance['state'] == 'running':
                    instance_type = instance['type']
                    hourly_cost = instance_costs.get(instance_type, 0.05)  # Default fallback
                    total_hourly += hourly_cost
            
            return {
                'hourly': round(total_hourly, 4),
                'daily': round(total_hourly * 24, 2),
                'monthly': round(total_hourly * 24 * 30, 2),
                'currency': 'USD'
            }
        except Exception as e:
            print(f"Error calculating costs: {e}")
            return {}
    
    def generate_report(self):
        """Generate a comprehensive infrastructure report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'aws_instances': self.get_aws_instances(),
            'terraform_state': self.get_terraform_state(),
            'ansible_status': self.get_ansible_status(),
            'system_metrics': self.get_system_metrics(),
            'cost_estimate': self.get_cost_estimate()
        }
        
        return report
    
    def save_report(self, report, filename=None):
        """Save report to file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"infraops_report_{timestamp}.json"
        
        reports_dir = self.project_root / 'reports'
        reports_dir.mkdir(exist_ok=True)
        
        report_path = reports_dir / filename
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report_path

def main():
    """Main function"""
    monitor = InfraOpsMonitor()
    
    print("🔍 InfraOps Infrastructure Monitor")
    print("=" * 40)
    
    # Generate report
    report = monitor.generate_report()
    
    # Display summary
    print(f"📊 Report generated at: {report['timestamp']}")
    print(f"🖥️  AWS Instances: {len(report['aws_instances'])}")
    
    running_instances = [i for i in report['aws_instances'] if i['state'] == 'running']
    print(f"✅ Running Instances: {len(running_instances)}")
    
    if report['cost_estimate']:
        print(f"💰 Estimated Monthly Cost: ${report['cost_estimate']['monthly']}")
    
    # Save report
    report_path = monitor.save_report(report)
    print(f"📄 Report saved to: {report_path}")
    
    # Display running instances
    if running_instances:
        print("\n🚀 Running Instances:")
        for instance in running_instances:
            name = instance['tags'].get('Name', instance['id'])
            print(f"  • {name} ({instance['type']}) - {instance['public_ip']}")

if __name__ == "__main__":
    main()
