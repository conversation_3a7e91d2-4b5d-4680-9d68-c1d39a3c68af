module.web_server.data.aws_availability_zones.available: Reading...
module.web_server.data.aws_vpc.default: Reading...
module.web_server.data.aws_ami.ubuntu: Reading...
module.web_server.data.aws_region.current: Reading...
module.web_server.data.aws_region.current: Read complete after 0s [id=us-west-2]
module.web_server.data.aws_availability_zones.available: Read complete after 1s [id=us-west-2]
module.web_server.data.aws_ami.ubuntu: Read complete after 2s [id=ami-0ac098a0168eb72d0]
module.web_server.data.aws_vpc.default: Read complete after 3s [id=vpc-03b35800b258e75cd]
module.web_server.data.aws_subnet.default: Reading...
module.web_server.data.aws_subnet.default: Read complete after 1s [id=subnet-0c80f795b3ae8d8bb]

Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  + create

Terraform will perform the following actions:

  # module.web_server.aws_instance.infraops will be created
  + resource "aws_instance" "infraops" {
      + ami                                  = "ami-0ac098a0168eb72d0"
      + arn                                  = (known after apply)
      + associate_public_ip_address          = (known after apply)
      + availability_zone                    = (known after apply)
      + cpu_core_count                       = (known after apply)
      + cpu_threads_per_core                 = (known after apply)
      + disable_api_stop                     = (known after apply)
      + disable_api_termination              = false
      + ebs_optimized                        = (known after apply)
      + enable_primary_ipv6                  = (known after apply)
      + get_password_data                    = false
      + host_id                              = (known after apply)
      + host_resource_group_arn              = (known after apply)
      + iam_instance_profile                 = (known after apply)
      + id                                   = (known after apply)
      + instance_initiated_shutdown_behavior = (known after apply)
      + instance_lifecycle                   = (known after apply)
      + instance_state                       = (known after apply)
      + instance_type                        = "t3.micro"
      + ipv6_address_count                   = (known after apply)
      + ipv6_addresses                       = (known after apply)
      + key_name                             = "infraops-dev-key"
      + monitoring                           = false
      + outpost_arn                          = (known after apply)
      + password_data                        = (known after apply)
      + placement_group                      = (known after apply)
      + placement_partition_number           = (known after apply)
      + primary_network_interface_id         = (known after apply)
      + private_dns                          = (known after apply)
      + private_ip                           = (known after apply)
      + public_dns                           = (known after apply)
      + public_ip                            = (known after apply)
      + secondary_private_ips                = (known after apply)
      + security_groups                      = (known after apply)
      + source_dest_check                    = true
      + spot_instance_request_id             = (known after apply)
      + subnet_id                            = "subnet-0c80f795b3ae8d8bb"
      + tags                                 = {
          + "CreatedBy"   = "infraops-agent"
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-server"
          + "Project"     = "infraops"
        }
      + tags_all                             = {
          + "CreatedBy"   = "infraops-agent"
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-server"
          + "Owner"       = "samruddhi"
          + "Project"     = "infraops"
        }
      + tenancy                              = (known after apply)
      + user_data_base64                     = (known after apply)
      + user_data_replace_on_change          = false
      + vpc_security_group_ids               = (known after apply)
        # (1 unchanged attribute hidden)

      + capacity_reservation_specification (known after apply)

      + cpu_options (known after apply)

      + ebs_block_device (known after apply)

      + enclave_options (known after apply)

      + ephemeral_block_device (known after apply)

      + instance_market_options (known after apply)

      + maintenance_options (known after apply)

      + metadata_options (known after apply)

      + network_interface (known after apply)

      + private_dns_name_options (known after apply)

      + root_block_device {
          + delete_on_termination = true
          + device_name           = (known after apply)
          + encrypted             = true
          + iops                  = (known after apply)
          + kms_key_id            = (known after apply)
          + tags                  = {
              + "Environment" = "dev"
              + "ManagedBy"   = "terraform"
              + "Name"        = "infraops-dev-server-root"
              + "Project"     = "infraops"
            }
          + tags_all              = (known after apply)
          + throughput            = (known after apply)
          + volume_id             = (known after apply)
          + volume_size           = 20
          + volume_type           = "gp3"
        }
    }

  # module.web_server.aws_key_pair.infraops will be created
  + resource "aws_key_pair" "infraops" {
      + arn             = (known after apply)
      + fingerprint     = (known after apply)
      + id              = (known after apply)
      + key_name        = "infraops-dev-key"
      + key_name_prefix = (known after apply)
      + key_pair_id     = (known after apply)
      + key_type        = (known after apply)
      + public_key      = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCrEJRtd8ualphJ+n2Ml27iLHjafnLwiJreUDtjnC+rr+TganyIP3Eg0u0avJA4IeFx7O+a8W9NphQ6NQQPjogxffZENpmmd0n+9ffjWAh9QIgm01yLvztjJTao929D2PuXOhQpPIk3mPvms/rUR/yzmktsmdi3r6Tewf2SfTUBnb8MB346pVZFq5/Arw6RcsPbdTkc6UKMfWbRhGt3x4ImmKXZdjuRzbEWJnoays7WxY/8wy35DROAwCMLCxxa8Bl0H72n2SkehPi1HL1y6dlb2d9xIA631TEEzwlrjjyXqMNXEBHjeFN3p5eAs1pLpMRGMEX3Fh0//x4d6mefwzuedc6US1PWzsy6fBag5rHOcDoVnuMj56KxlKbUPUAOW6cn2NpTa+RUV+ftijy7hIWeNq8mv50S/+FRL/HuTR3GbFy6Ho1R9h+MuJ66WguvgW97JMGBhvj8QhF7hX0wFSB01B66sR3g4XmotFUrZqiPFBkmeBvYF2AEXTJTh0yhPRXoTyeoW1z+LAIwC2/w9C1XLMQM47REGU3kunHKQwu7GsNO3Nb2jt1CWwv0gqMrCJt5fta9JGVhJOyLi5kaQoKu88zxmF0y7LA0v2Y75oYN1H9XSmIiw3KFj9R1Ki5Z7aUk6UDbSvhH5XZvRsyp6s+6fkPaXvZdUT3dsch14ez5rw== infraops-agent"
      + tags            = {
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-key"
          + "Project"     = "infraops"
        }
      + tags_all        = {
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-key"
          + "Owner"       = "samruddhi"
          + "Project"     = "infraops"
        }
    }

  # module.web_server.aws_security_group.infraops will be created
  + resource "aws_security_group" "infraops" {
      + arn                    = (known after apply)
      + description            = "Security group for InfraOps dev environment"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
                # (1 unchanged attribute hidden)
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "HTTP"
              + from_port        = 80
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 80
            },
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "HTTPS"
              + from_port        = 443
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 443
            },
          + {
              + cidr_blocks      = [
                  + "**************/32",
                ]
              + description      = "SSH"
              + from_port        = 22
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 22
            },
        ]
      + name                   = (known after apply)
      + name_prefix            = "infraops-dev-"
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-sg"
          + "Project"     = "infraops"
        }
      + tags_all               = {
          + "Environment" = "dev"
          + "ManagedBy"   = "terraform"
          + "Name"        = "infraops-dev-sg"
          + "Owner"       = "samruddhi"
          + "Project"     = "infraops"
        }
      + vpc_id                 = "vpc-03b35800b258e75cd"
    }

Plan: 3 to add, 0 to change, 0 to destroy.

Changes to Outputs:
  + ansible_host        = (sensitive value)
  + infrastructure_info = {
      + ansible     = {
          + inventory = {
              + web_servers = {
                  + hosts = {
                      + infraops-dev-server = {
                          + ansible_host                 = (known after apply)
                          + ansible_ssh_private_key_file = "~/.ssh/infraops_key"
                          + ansible_user                 = "ubuntu"
                          + instance_id                  = (known after apply)
                          + private_ip                   = (known after apply)
                        }
                    }
                }
            }
        }
      + environment = "dev"
      + instances   = {
          + web_server = {
              + az         = (known after apply)
              + id         = (known after apply)
              + private_ip = (known after apply)
              + public_ip  = (known after apply)
              + type       = "t3.micro"
            }
        }
      + region      = "us-west-2"
    }
  + instance_id         = (known after apply)
  + instance_summary    = {
      + ami_id            = "ami-0ac098a0168eb72d0"
      + availability_zone = (known after apply)
      + created_at        = (known after apply)
      + environment       = "dev"
      + instance_id       = (known after apply)
      + instance_type     = "t3.micro"
      + name              = "infraops-dev-server"
      + private_ip        = (known after apply)
      + project           = "infraops"
      + public_ip         = (known after apply)
      + region            = "us-west-2"
    }
  + key_pair_name       = "infraops-dev-key"
  + private_ip          = (known after apply)
  + public_dns          = (known after apply)
  + public_ip           = (known after apply)
  + security_group_id   = (known after apply)

─────────────────────────────────────────────────────────────────────────────

Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
