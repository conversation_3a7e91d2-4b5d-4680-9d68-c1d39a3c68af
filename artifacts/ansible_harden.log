Playbook: harden.yml
Return code: 0
================================================================================
STDOUT:
[0;34mNo config file found; using defaults[0m

PLAY [Security Hardening] ******************************************************

TASK [Gathering Facts] *********************************************************
[0;32mok: [web_server][0m

TASK [Install UFW firewall] ****************************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cache_update_time: 1755775719[0m
[0;32m  cache_updated: false[0m

TASK [Reset UFW to defaults] ***************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  commands:[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  - /usr/sbin/ufw -f reset[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  msg: 'Status: inactive'[0m

TASK [Configure UFW defaults] **************************************************
[0;32mok: [web_server] => (item={'direction': 'incoming', 'policy': 'deny'}) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  commands:[0m
[0;32m  - /usr/sbin/ufw status verbose[0m
[0;32m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;32m  - /usr/sbin/ufw default deny incoming[0m
[0;32m  - /usr/sbin/ufw status verbose[0m
[0;32m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;32m  item:[0m
[0;32m    direction: incoming[0m
[0;32m    policy: deny[0m
[0;32m  msg: 'Status: inactive'[0m
[0;32mok: [web_server] => (item={'direction': 'outgoing', 'policy': 'allow'}) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  commands:[0m
[0;32m  - /usr/sbin/ufw status verbose[0m
[0;32m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;32m  - /usr/sbin/ufw default allow outgoing[0m
[0;32m  - /usr/sbin/ufw status verbose[0m
[0;32m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;32m  item:[0m
[0;32m    direction: outgoing[0m
[0;32m    policy: allow[0m
[0;32m  msg: 'Status: inactive'[0m

TASK [Allow SSH through UFW] ***************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  commands:[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  - /usr/sbin/ufw --version[0m
[0;33m  - /usr/sbin/ufw allow from any to any port 22 proto tcp[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  msg: 'Status: inactive'[0m

TASK [Allow HTTP through UFW] **************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  commands:[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  - /usr/sbin/ufw --version[0m
[0;33m  - /usr/sbin/ufw allow from any to any port 80 proto tcp[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  msg: 'Status: inactive'[0m

TASK [Allow HTTPS through UFW] *************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  commands:[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  - /usr/sbin/ufw --version[0m
[0;33m  - /usr/sbin/ufw allow from any to any port 443 proto tcp[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  msg: 'Status: inactive'[0m

TASK [Enable UFW] **************************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  commands:[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  - /usr/sbin/ufw -f enable[0m
[0;33m  - /usr/sbin/ufw status verbose[0m
[0;33m  - /usr/bin/grep -h '^### tuple' /lib/ufw/user.rules /lib/ufw/user6.rules /etc/ufw/user.rules /etc/ufw/user6.rules /var/lib/ufw/user.rules /var/lib/ufw/user6.rules[0m
[0;33m  msg: |-[0m
[0;33m    Status: active[0m
[0;33m    Logging: on (low)[0m
[0;33m    Default: deny (incoming), allow (outgoing), disabled (routed)[0m
[0;33m    New profiles: skip[0m
[0;33m  [0m
[0;33m    To                         Action      From[0m
[0;33m    --                         ------      ----[0m
[0;33m    22/tcp                     ALLOW IN    Anywhere[0m
[0;33m    80/tcp                     ALLOW IN    Anywhere[0m
[0;33m    443/tcp                    ALLOW IN    Anywhere[0m
[0;33m    22/tcp (v6)                ALLOW IN    Anywhere (v6)[0m
[0;33m    80/tcp (v6)                ALLOW IN    Anywhere (v6)[0m
[0;33m    443/tcp (v6)               ALLOW IN    Anywhere (v6)[0m

TASK [Backup original SSH config] **********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: df0dda58a51bf6f92008e2f2ab5be22a3e64e0c2[0m
[0;33m  dest: /etc/ssh/sshd_config.backup[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: 30e0fe758429c57d35a5e71dbd8dd2f8[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 3254[0m
[0;33m  src: /etc/ssh/sshd_config[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Configure SSH hardening] *************************************************
[0;33mchanged: [web_server] => (item={'regexp': '^#?PermitRootLogin', 'line': 'PermitRootLogin no'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.9985.2025-08-21@11:33:06~[0m
[0;33m  item:[0m
[0;33m    line: PermitRootLogin no[0m
[0;33m    regexp: ^#?PermitRootLogin[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?PasswordAuthentication', 'line': 'PasswordAuthentication no'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10010.2025-08-21@11:33:12~[0m
[0;33m  item:[0m
[0;33m    line: PasswordAuthentication no[0m
[0;33m    regexp: ^#?PasswordAuthentication[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?PubkeyAuthentication', 'line': 'PubkeyAuthentication yes'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10035.2025-08-21@11:33:19~[0m
[0;33m  item:[0m
[0;33m    line: PubkeyAuthentication yes[0m
[0;33m    regexp: ^#?PubkeyAuthentication[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?AuthorizedKeysFile', 'line': 'AuthorizedKeysFile .ssh/authorized_keys'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10060.2025-08-21@11:33:25~[0m
[0;33m  item:[0m
[0;33m    line: AuthorizedKeysFile .ssh/authorized_keys[0m
[0;33m    regexp: ^#?AuthorizedKeysFile[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?PermitEmptyPasswords', 'line': 'PermitEmptyPasswords no'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10085.2025-08-21@11:33:31~[0m
[0;33m  item:[0m
[0;33m    line: PermitEmptyPasswords no[0m
[0;33m    regexp: ^#?PermitEmptyPasswords[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?ChallengeResponseAuthentication', 'line': 'ChallengeResponseAuthentication no'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10110.2025-08-21@11:33:37~[0m
[0;33m  item:[0m
[0;33m    line: ChallengeResponseAuthentication no[0m
[0;33m    regexp: ^#?ChallengeResponseAuthentication[0m
[0;33m  msg: line added[0m
[0;32mok: [web_server] => (item={'regexp': '^#?UsePAM', 'line': 'UsePAM yes'}) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  backup: ''[0m
[0;32m  item:[0m
[0;32m    line: UsePAM yes[0m
[0;32m    regexp: ^#?UsePAM[0m
[0;32m  msg: ''[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?X11Forwarding', 'line': 'X11Forwarding no'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10158.2025-08-21@11:33:50~[0m
[0;33m  item:[0m
[0;33m    line: X11Forwarding no[0m
[0;33m    regexp: ^#?X11Forwarding[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?ClientAliveInterval', 'line': 'ClientAliveInterval 300'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10183.2025-08-21@11:33:56~[0m
[0;33m  item:[0m
[0;33m    line: ClientAliveInterval 300[0m
[0;33m    regexp: ^#?ClientAliveInterval[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?ClientAliveCountMax', 'line': 'ClientAliveCountMax 2'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10208.2025-08-21@11:34:02~[0m
[0;33m  item:[0m
[0;33m    line: ClientAliveCountMax 2[0m
[0;33m    regexp: ^#?ClientAliveCountMax[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?MaxAuthTries', 'line': 'MaxAuthTries 3'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10233.2025-08-21@11:34:08~[0m
[0;33m  item:[0m
[0;33m    line: MaxAuthTries 3[0m
[0;33m    regexp: ^#?MaxAuthTries[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?MaxSessions', 'line': 'MaxSessions 2'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10258.2025-08-21@11:34:14~[0m
[0;33m  item:[0m
[0;33m    line: MaxSessions 2[0m
[0;33m    regexp: ^#?MaxSessions[0m
[0;33m  msg: line replaced[0m
[0;33mchanged: [web_server] => (item={'regexp': '^#?Protocol', 'line': 'Protocol 2'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: /etc/ssh/sshd_config.10283.2025-08-21@11:34:21~[0m
[0;33m  item:[0m
[0;33m    line: Protocol 2[0m
[0;33m    regexp: ^#?Protocol[0m
[0;33m  msg: line added[0m

TASK [Configure allowed SSH users] *********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  backup: /etc/ssh/sshd_config.10308.2025-08-21@11:34:27~[0m
[0;33m  msg: line added[0m

TASK [Disable unused network protocols] ****************************************
[0;33mchanged: [web_server] => (item=install dccp /bin/true) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: ''[0m
[0;33m  item: install dccp /bin/true[0m
[0;33m  msg: line added[0m
[0;33mchanged: [web_server] => (item=install sctp /bin/true) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: ''[0m
[0;33m  item: install sctp /bin/true[0m
[0;33m  msg: line added[0m
[0;33mchanged: [web_server] => (item=install rds /bin/true) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: ''[0m
[0;33m  item: install rds /bin/true[0m
[0;33m  msg: line added[0m
[0;33mchanged: [web_server] => (item=install tipc /bin/true) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  backup: ''[0m
[0;33m  item: install tipc /bin/true[0m
[0;33m  msg: line added[0m

TASK [Configure kernel parameters for security] ********************************
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.ip_forward', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.ip_forward[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.all.send_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.all.send_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.default.send_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.default.send_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.all.accept_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.all.accept_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.default.accept_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.default.accept_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.all.secure_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.all.secure_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.default.secure_redirects', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.default.secure_redirects[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.all.accept_source_route', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.all.accept_source_route[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.default.accept_source_route', 'value': '0'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.default.accept_source_route[0m
[0;33m    value: '0'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.all.log_martians', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.all.log_martians[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.conf.default.log_martians', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.conf.default.log_martians[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.icmp_echo_ignore_broadcasts', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.icmp_echo_ignore_broadcasts[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.icmp_ignore_bogus_error_responses', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.icmp_ignore_bogus_error_responses[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'net.ipv4.tcp_syncookies', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: net.ipv4.tcp_syncookies[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'kernel.dmesg_restrict', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: kernel.dmesg_restrict[0m
[0;33m    value: '1'[0m
[0;33mchanged: [web_server] => (item={'name': 'kernel.kptr_restrict', 'value': '2'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: kernel.kptr_restrict[0m
[0;33m    value: '2'[0m
[0;33mchanged: [web_server] => (item={'name': 'kernel.yama.ptrace_scope', 'value': '1'}) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item:[0m
[0;33m    name: kernel.yama.ptrace_scope[0m
[0;33m    value: '1'[0m

TASK [Configure fail2ban for SSH] **********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: 1f6ccb01953a949cbb398d83244e472d81db0a3d[0m
[0;33m  dest: /etc/fail2ban/jail.d/sshd.conf[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: 548cb1a17cad7af0461a85e29bef9e7f[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 117[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776202.4863727-28405-46166081651444/.source.conf[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Remove unnecessary packages] *********************************************
[0;33mchanged: [web_server] => (item=telnet) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  item: telnet[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following packages will be REMOVED:[0m
[0;33m      telnet[0m
[0;33m    0 upgraded, 0 newly installed, 1 to remove and 0 not upgraded.[0m
[0;33m    After this operation, 158 kB disk space will be freed.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 103885 files and directories currently installed.)[0m
[0;33m    Removing telnet (0.17-44build1) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m  stdout_lines: <omitted>[0m
[0;32mok: [web_server] => (item=rsh-client) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  item: rsh-client[0m
[0;32mok: [web_server] => (item=rsh-redone-client) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  item: rsh-redone-client[0m
[0;32mok: [web_server] => (item=talk) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  item: talk[0m
[0;32mok: [web_server] => (item=ntalk) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  item: ntalk[0m

TASK [Set up automatic security updates] ***************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  backup_file: /etc/apt/apt.conf.d/20auto-upgrades.11103.2025-08-21@11:37:50~[0m
[0;33m  checksum: eac74547eec217a356899a6d8a377d3f1522851a[0m
[0;33m  dest: /etc/apt/apt.conf.d/20auto-upgrades[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: 4e7d1cf7c590a703ad853f2658fb8eeb[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 168[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776257.162161-28481-197792717374299/.source[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Configure logrotate for security logs] ***********************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: 50b88c8d8899872a45a07d2636e1d6636dc74e0e[0m
[0;33m  dest: /etc/logrotate.d/auth[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: 0fd3c494e66aaafbbc03f978ae0b23a9[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 131[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776271.844748-28504-40938653047779/.source[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Display hardening completion message] ************************************
[0;32mok: [web_server] => [0m
[0;32m  msg: |-[0m
[0;32m    Security hardening completed successfully![0m
[0;32m  [0m
[0;32m    Applied configurations:[0m
[0;32m    - UFW firewall enabled (SSH, HTTP, HTTPS allowed)[0m
[0;32m    - SSH hardened (root login disabled, key-only auth)[0m
[0;32m    - Kernel security parameters configured[0m
[0;32m    - Fail2ban configured for SSH protection[0m
[0;32m    - Unnecessary packages removed[0m
[0;32m    - Automatic security updates enabled[0m
[0;32m  [0m
[0;32m    Important: SSH service will be restarted to apply changes.[0m
[0;32m    Make sure you have SSH key access before disconnecting![0m

RUNNING HANDLER [restart ssh] **************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  name: ssh[0m
[0;33m  state: started[0m
[0;33m  status:[0m
[0;33m    ActiveEnterTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    ActiveEnterTimestampMonotonic: '********'[0m
[0;33m    ActiveExitTimestamp: n/a[0m
[0;33m    ActiveExitTimestampMonotonic: '0'[0m
[0;33m    ActiveState: active[0m
[0;33m    After: systemd-journald.socket system.slice ec2-instance-connect.service basic.target auditd.service cloud-init.service -.mount sysinit.target network.target pollinate.service[0m
[0;33m    AllowIsolate: 'no'[0m
[0;33m    AssertResult: 'yes'[0m
[0;33m    AssertTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    AssertTimestampMonotonic: '********'[0m
[0;33m    Before: shutdown.target multi-user.target[0m
[0;33m    BlockIOAccounting: 'no'[0m
[0;33m    BlockIOWeight: '[not set]'[0m
[0;33m    CPUAccounting: 'yes'[0m
[0;33m    CPUAffinityFromNUMA: 'no'[0m
[0;33m    CPUQuotaPerSecUSec: infinity[0m
[0;33m    CPUQuotaPeriodUSec: infinity[0m
[0;33m    CPUSchedulingPolicy: '0'[0m
[0;33m    CPUSchedulingPriority: '0'[0m
[0;33m    CPUSchedulingResetOnFork: 'no'[0m
[0;33m    CPUShares: '[not set]'[0m
[0;33m    CPUUsageNSec: '********'[0m
[0;33m    CPUWeight: '[not set]'[0m
[0;33m    CacheDirectoryMode: '0755'[0m
[0;33m    CanClean: runtime[0m
[0;33m    CanFreeze: 'yes'[0m
[0;33m    CanIsolate: 'no'[0m
[0;33m    CanReload: 'yes'[0m
[0;33m    CanStart: 'yes'[0m
[0;33m    CanStop: 'yes'[0m
[0;33m    CapabilityBoundingSet: cap_chown cap_dac_override cap_dac_read_search cap_fowner cap_fsetid cap_kill cap_setgid cap_setuid cap_setpcap cap_linux_immutable cap_net_bind_service cap_net_broadcast cap_net_admin cap_net_raw cap_ipc_lock cap_ipc_owner cap_sys_module cap_sys_rawio cap_sys_chroot cap_sys_ptrace cap_sys_pacct cap_sys_admin cap_sys_boot cap_sys_nice cap_sys_resource cap_sys_time cap_sys_tty_config cap_mknod cap_lease cap_audit_write cap_audit_control cap_setfcap cap_mac_override cap_mac_admin cap_syslog cap_wake_alarm cap_block_suspend cap_audit_read cap_perfmon cap_bpf cap_checkpoint_restore[0m
[0;33m    CleanResult: success[0m
[0;33m    CollectMode: inactive[0m
[0;33m    ConditionResult: 'yes'[0m
[0;33m    ConditionTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    ConditionTimestampMonotonic: '11111573'[0m
[0;33m    ConfigurationDirectoryMode: '0755'[0m
[0;33m    Conflicts: shutdown.target[0m
[0;33m    ControlGroup: /system.slice/ssh.service[0m
[0;33m    ControlPID: '0'[0m
[0;33m    CoredumpFilter: '0x33'[0m
[0;33m    DefaultDependencies: 'yes'[0m
[0;33m    DefaultMemoryLow: '0'[0m
[0;33m    DefaultMemoryMin: '0'[0m
[0;33m    Delegate: 'no'[0m
[0;33m    Description: OpenBSD Secure Shell server[0m
[0;33m    DevicePolicy: auto[0m
[0;33m    Documentation: '"man:sshd(8)" "man:sshd_config(5)"'[0m
[0;33m    DropInPaths: /usr/lib/systemd/system/ssh.service.d/ec2-instance-connect.conf[0m
[0;33m    DynamicUser: 'no'[0m
[0;33m    EffectiveCPUs: 0-1[0m
[0;33m    EffectiveMemoryNodes: '0'[0m
[0;33m    EnvironmentFiles: /etc/default/ssh (ignore_errors=yes)[0m
[0;33m    ExecMainCode: '0'[0m
[0;33m    ExecMainExitTimestamp: n/a[0m
[0;33m    ExecMainExitTimestampMonotonic: '0'[0m
[0;33m    ExecMainPID: '667'[0m
[0;33m    ExecMainStartTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    ExecMainStartTimestampMonotonic: '11178130'[0m
[0;33m    ExecMainStatus: '0'[0m
[0;33m    ExecReload: '{ path=/bin/kill ; argv[]=/bin/kill -HUP $MAINPID ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecReloadEx: '{ path=/bin/kill ; argv[]=/bin/kill -HUP $MAINPID ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStart: '{ path=/usr/sbin/sshd ; argv[]=/usr/sbin/sshd -D -o AuthorizedKeysCommand /usr/share/ec2-instance-connect/eic_run_authorized_keys %u %f -o AuthorizedKeysCommandUser ec2-instance-connect $SSHD_OPTS ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStartEx: '{ path=/usr/sbin/sshd ; argv[]=/usr/sbin/sshd -D -o AuthorizedKeysCommand /usr/share/ec2-instance-connect/eic_run_authorized_keys %u %f -o AuthorizedKeysCommandUser ec2-instance-connect $SSHD_OPTS ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStartPre: '{ path=/usr/sbin/sshd ; argv[]=/usr/sbin/sshd -t ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStartPreEx: '{ path=/usr/sbin/sshd ; argv[]=/usr/sbin/sshd -t ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    FailureAction: none[0m
[0;33m    FileDescriptorStoreMax: '0'[0m
[0;33m    FinalKillSignal: '9'[0m
[0;33m    FragmentPath: /lib/systemd/system/ssh.service[0m
[0;33m    FreezerState: running[0m
[0;33m    GID: '[not set]'[0m
[0;33m    GuessMainPID: 'yes'[0m
[0;33m    IOAccounting: 'no'[0m
[0;33m    IOReadBytes: '18446744073709551615'[0m
[0;33m    IOReadOperations: '18446744073709551615'[0m
[0;33m    IOSchedulingClass: '2'[0m
[0;33m    IOSchedulingPriority: '4'[0m
[0;33m    IOWeight: '[not set]'[0m
[0;33m    IOWriteBytes: '18446744073709551615'[0m
[0;33m    IOWriteOperations: '18446744073709551615'[0m
[0;33m    IPAccounting: 'no'[0m
[0;33m    IPEgressBytes: '[no data]'[0m
[0;33m    IPEgressPackets: '[no data]'[0m
[0;33m    IPIngressBytes: '[no data]'[0m
[0;33m    IPIngressPackets: '[no data]'[0m
[0;33m    Id: ssh.service[0m
[0;33m    IgnoreOnIsolate: 'no'[0m
[0;33m    IgnoreSIGPIPE: 'yes'[0m
[0;33m    InactiveEnterTimestamp: n/a[0m
[0;33m    InactiveEnterTimestampMonotonic: '0'[0m
[0;33m    InactiveExitTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    InactiveExitTimestampMonotonic: '********'[0m
[0;33m    InvocationID: c42978e4cc684ecd8864a53747fa81f4[0m
[0;33m    JobRunningTimeoutUSec: infinity[0m
[0;33m    JobTimeoutAction: none[0m
[0;33m    JobTimeoutUSec: infinity[0m
[0;33m    KeyringMode: private[0m
[0;33m    KillMode: process[0m
[0;33m    KillSignal: '15'[0m
[0;33m    LimitAS: infinity[0m
[0;33m    LimitASSoft: infinity[0m
[0;33m    LimitCORE: infinity[0m
[0;33m    LimitCORESoft: '0'[0m
[0;33m    LimitCPU: infinity[0m
[0;33m    LimitCPUSoft: infinity[0m
[0;33m    LimitDATA: infinity[0m
[0;33m    LimitDATASoft: infinity[0m
[0;33m    LimitFSIZE: infinity[0m
[0;33m    LimitFSIZESoft: infinity[0m
[0;33m    LimitLOCKS: infinity[0m
[0;33m    LimitLOCKSSoft: infinity[0m
[0;33m    LimitMEMLOCK: '8388608'[0m
[0;33m    LimitMEMLOCKSoft: '8388608'[0m
[0;33m    LimitMSGQUEUE: '819200'[0m
[0;33m    LimitMSGQUEUESoft: '819200'[0m
[0;33m    LimitNICE: '0'[0m
[0;33m    LimitNICESoft: '0'[0m
[0;33m    LimitNOFILE: '524288'[0m
[0;33m    LimitNOFILESoft: '1024'[0m
[0;33m    LimitNPROC: '3577'[0m
[0;33m    LimitNPROCSoft: '3577'[0m
[0;33m    LimitRSS: infinity[0m
[0;33m    LimitRSSSoft: infinity[0m
[0;33m    LimitRTPRIO: '0'[0m
[0;33m    LimitRTPRIOSoft: '0'[0m
[0;33m    LimitRTTIME: infinity[0m
[0;33m    LimitRTTIMESoft: infinity[0m
[0;33m    LimitSIGPENDING: '3577'[0m
[0;33m    LimitSIGPENDINGSoft: '3577'[0m
[0;33m    LimitSTACK: infinity[0m
[0;33m    LimitSTACKSoft: '8388608'[0m
[0;33m    LoadState: loaded[0m
[0;33m    LockPersonality: 'no'[0m
[0;33m    LogLevelMax: '-1'[0m
[0;33m    LogRateLimitBurst: '0'[0m
[0;33m    LogRateLimitIntervalUSec: '0'[0m
[0;33m    LogsDirectoryMode: '0755'[0m
[0;33m    MainPID: '667'[0m
[0;33m    ManagedOOMMemoryPressure: auto[0m
[0;33m    ManagedOOMMemoryPressureLimit: '0'[0m
[0;33m    ManagedOOMPreference: none[0m
[0;33m    ManagedOOMSwap: auto[0m
[0;33m    MemoryAccounting: 'yes'[0m
[0;33m    MemoryAvailable: infinity[0m
[0;33m    MemoryCurrent: '6189056'[0m
[0;33m    MemoryDenyWriteExecute: 'no'[0m
[0;33m    MemoryHigh: infinity[0m
[0;33m    MemoryLimit: infinity[0m
[0;33m    MemoryLow: '0'[0m
[0;33m    MemoryMax: infinity[0m
[0;33m    MemoryMin: '0'[0m
[0;33m    MemorySwapMax: infinity[0m
[0;33m    MountAPIVFS: 'no'[0m
[0;33m    NFileDescriptorStore: '0'[0m
[0;33m    NRestarts: '0'[0m
[0;33m    NUMAPolicy: n/a[0m
[0;33m    Names: ssh.service sshd.service[0m
[0;33m    NeedDaemonReload: 'no'[0m
[0;33m    Nice: '0'[0m
[0;33m    NoNewPrivileges: 'no'[0m
[0;33m    NonBlocking: 'no'[0m
[0;33m    NotifyAccess: main[0m
[0;33m    OOMPolicy: stop[0m
[0;33m    OOMScoreAdjust: '0'[0m
[0;33m    OnFailureJobMode: replace[0m
[0;33m    OnSuccessJobMode: fail[0m
[0;33m    Perpetual: 'no'[0m
[0;33m    PrivateDevices: 'no'[0m
[0;33m    PrivateIPC: 'no'[0m
[0;33m    PrivateMounts: 'no'[0m
[0;33m    PrivateNetwork: 'no'[0m
[0;33m    PrivateTmp: 'no'[0m
[0;33m    PrivateUsers: 'no'[0m
[0;33m    ProcSubset: all[0m
[0;33m    ProtectClock: 'no'[0m
[0;33m    ProtectControlGroups: 'no'[0m
[0;33m    ProtectHome: 'no'[0m
[0;33m    ProtectHostname: 'no'[0m
[0;33m    ProtectKernelLogs: 'no'[0m
[0;33m    ProtectKernelModules: 'no'[0m
[0;33m    ProtectKernelTunables: 'no'[0m
[0;33m    ProtectProc: default[0m
[0;33m    ProtectSystem: 'no'[0m
[0;33m    RefuseManualStart: 'no'[0m
[0;33m    RefuseManualStop: 'no'[0m
[0;33m    ReloadResult: success[0m
[0;33m    RemainAfterExit: 'no'[0m
[0;33m    RemoveIPC: 'no'[0m
[0;33m    Requires: -.mount system.slice sysinit.target[0m
[0;33m    RequiresMountsFor: /run/sshd[0m
[0;33m    Restart: on-failure[0m
[0;33m    RestartKillSignal: '15'[0m
[0;33m    RestartPreventExitStatus: '255'[0m
[0;33m    RestartUSec: 100ms[0m
[0;33m    RestrictNamespaces: 'no'[0m
[0;33m    RestrictRealtime: 'no'[0m
[0;33m    RestrictSUIDSGID: 'no'[0m
[0;33m    Result: success[0m
[0;33m    RootDirectoryStartOnly: 'no'[0m
[0;33m    RuntimeDirectory: sshd[0m
[0;33m    RuntimeDirectoryMode: '0755'[0m
[0;33m    RuntimeDirectoryPreserve: 'no'[0m
[0;33m    RuntimeMaxUSec: infinity[0m
[0;33m    SameProcessGroup: 'no'[0m
[0;33m    SecureBits: '0'[0m
[0;33m    SendSIGHUP: 'no'[0m
[0;33m    SendSIGKILL: 'yes'[0m
[0;33m    Slice: system.slice[0m
[0;33m    StandardError: inherit[0m
[0;33m    StandardInput: 'null'[0m
[0;33m    StandardOutput: journal[0m
[0;33m    StartLimitAction: none[0m
[0;33m    StartLimitBurst: '5'[0m
[0;33m    StartLimitIntervalUSec: 10s[0m
[0;33m    StartupBlockIOWeight: '[not set]'[0m
[0;33m    StartupCPUShares: '[not set]'[0m
[0;33m    StartupCPUWeight: '[not set]'[0m
[0;33m    StartupIOWeight: '[not set]'[0m
[0;33m    StateChangeTimestamp: Thu 2025-08-21 11:22:28 UTC[0m
[0;33m    StateChangeTimestampMonotonic: '********'[0m
[0;33m    StateDirectoryMode: '0755'[0m
[0;33m    StatusErrno: '0'[0m
[0;33m    StopWhenUnneeded: 'no'[0m
[0;33m    SubState: running[0m
[0;33m    SuccessAction: none[0m
[0;33m    SyslogFacility: '3'[0m
[0;33m    SyslogLevel: '6'[0m
[0;33m    SyslogLevelPrefix: 'yes'[0m
[0;33m    SyslogPriority: '30'[0m
[0;33m    SystemCallErrorNumber: '**********'[0m
[0;33m    TTYReset: 'no'[0m
[0;33m    TTYVHangup: 'no'[0m
[0;33m    TTYVTDisallocate: 'no'[0m
[0;33m    TasksAccounting: 'yes'[0m
[0;33m    TasksCurrent: '1'[0m
[0;33m    TasksMax: '1073'[0m
[0;33m    TimeoutAbortUSec: 1min 30s[0m
[0;33m    TimeoutCleanUSec: infinity[0m
[0;33m    TimeoutStartFailureMode: terminate[0m
[0;33m    TimeoutStartUSec: 1min 30s[0m
[0;33m    TimeoutStopFailureMode: terminate[0m
[0;33m    TimeoutStopUSec: 1min 30s[0m
[0;33m    TimerSlackNSec: '50000'[0m
[0;33m    Transient: 'no'[0m
[0;33m    Type: notify[0m
[0;33m    UID: '[not set]'[0m
[0;33m    UMask: '0022'[0m
[0;33m    UnitFilePreset: enabled[0m
[0;33m    UnitFileState: enabled[0m
[0;33m    UtmpMode: init[0m
[0;33m    WantedBy: multi-user.target cloud-init.service[0m
[0;33m    WatchdogSignal: '6'[0m
[0;33m    WatchdogTimestamp: n/a[0m
[0;33m    WatchdogTimestampMonotonic: '0'[0m
[0;33m    WatchdogUSec: '0'[0m

RUNNING HANDLER [restart fail2ban] *********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  name: fail2ban[0m
[0;33m  state: started[0m
[0;33m  status:[0m
[0;33m    ActiveEnterTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    ActiveEnterTimestampMonotonic: '*********'[0m
[0;33m    ActiveExitTimestamp: n/a[0m
[0;33m    ActiveExitTimestampMonotonic: '0'[0m
[0;33m    ActiveState: active[0m
[0;33m    After: iptables.service systemd-journald.socket ip6tables.service firewalld.service network.target nftables.service -.mount sysinit.target basic.target system.slice ipset.service[0m
[0;33m    AllowIsolate: 'no'[0m
[0;33m    AssertResult: 'yes'[0m
[0;33m    AssertTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    AssertTimestampMonotonic: '*********'[0m
[0;33m    Before: shutdown.target multi-user.target[0m
[0;33m    BlockIOAccounting: 'no'[0m
[0;33m    BlockIOWeight: '[not set]'[0m
[0;33m    CPUAccounting: 'yes'[0m
[0;33m    CPUAffinityFromNUMA: 'no'[0m
[0;33m    CPUQuotaPerSecUSec: infinity[0m
[0;33m    CPUQuotaPeriodUSec: infinity[0m
[0;33m    CPUSchedulingPolicy: '0'[0m
[0;33m    CPUSchedulingPriority: '0'[0m
[0;33m    CPUSchedulingResetOnFork: 'no'[0m
[0;33m    CPUShares: '[not set]'[0m
[0;33m    CPUUsageNSec: '*********'[0m
[0;33m    CPUWeight: '[not set]'[0m
[0;33m    CacheDirectoryMode: '0755'[0m
[0;33m    CanClean: runtime[0m
[0;33m    CanFreeze: 'yes'[0m
[0;33m    CanIsolate: 'no'[0m
[0;33m    CanReload: 'yes'[0m
[0;33m    CanStart: 'yes'[0m
[0;33m    CanStop: 'yes'[0m
[0;33m    CapabilityBoundingSet: cap_chown cap_dac_override cap_dac_read_search cap_fowner cap_fsetid cap_kill cap_setgid cap_setuid cap_setpcap cap_linux_immutable cap_net_bind_service cap_net_broadcast cap_net_admin cap_net_raw cap_ipc_lock cap_ipc_owner cap_sys_module cap_sys_rawio cap_sys_chroot cap_sys_ptrace cap_sys_pacct cap_sys_admin cap_sys_boot cap_sys_nice cap_sys_resource cap_sys_time cap_sys_tty_config cap_mknod cap_lease cap_audit_write cap_audit_control cap_setfcap cap_mac_override cap_mac_admin cap_syslog cap_wake_alarm cap_block_suspend cap_audit_read cap_perfmon cap_bpf cap_checkpoint_restore[0m
[0;33m    CleanResult: success[0m
[0;33m    CollectMode: inactive[0m
[0;33m    ConditionResult: 'yes'[0m
[0;33m    ConditionTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    ConditionTimestampMonotonic: '546776124'[0m
[0;33m    ConfigurationDirectoryMode: '0755'[0m
[0;33m    Conflicts: shutdown.target[0m
[0;33m    ControlGroup: /system.slice/fail2ban.service[0m
[0;33m    ControlPID: '0'[0m
[0;33m    CoredumpFilter: '0x33'[0m
[0;33m    DefaultDependencies: 'yes'[0m
[0;33m    DefaultMemoryLow: '0'[0m
[0;33m    DefaultMemoryMin: '0'[0m
[0;33m    Delegate: 'no'[0m
[0;33m    Description: Fail2Ban Service[0m
[0;33m    DevicePolicy: auto[0m
[0;33m    Documentation: '"man:fail2ban(1)"'[0m
[0;33m    DynamicUser: 'no'[0m
[0;33m    EffectiveCPUs: 0-1[0m
[0;33m    EffectiveMemoryNodes: '0'[0m
[0;33m    Environment: PYTHONNOUSERSITE=yes[0m
[0;33m    ExecMainCode: '0'[0m
[0;33m    ExecMainExitTimestamp: n/a[0m
[0;33m    ExecMainExitTimestampMonotonic: '0'[0m
[0;33m    ExecMainPID: '9317'[0m
[0;33m    ExecMainStartTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    ExecMainStartTimestampMonotonic: '546782159'[0m
[0;33m    ExecMainStatus: '0'[0m
[0;33m    ExecReload: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client reload ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecReloadEx: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client reload ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStart: '{ path=/usr/bin/fail2ban-server ; argv[]=/usr/bin/fail2ban-server -xf start ; ignore_errors=no ; start_time=[Thu 2025-08-21 11:31:23 UTC] ; stop_time=[n/a] ; pid=9317 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStartEx: '{ path=/usr/bin/fail2ban-server ; argv[]=/usr/bin/fail2ban-server -xf start ; flags= ; start_time=[Thu 2025-08-21 11:31:23 UTC] ; stop_time=[n/a] ; pid=9317 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStop: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client stop ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStopEx: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client stop ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    FailureAction: none[0m
[0;33m    FileDescriptorStoreMax: '0'[0m
[0;33m    FinalKillSignal: '9'[0m
[0;33m    FragmentPath: /lib/systemd/system/fail2ban.service[0m
[0;33m    FreezerState: running[0m
[0;33m    GID: '[not set]'[0m
[0;33m    GuessMainPID: 'yes'[0m
[0;33m    IOAccounting: 'no'[0m
[0;33m    IOReadBytes: '18446744073709551615'[0m
[0;33m    IOReadOperations: '18446744073709551615'[0m
[0;33m    IOSchedulingClass: '2'[0m
[0;33m    IOSchedulingPriority: '4'[0m
[0;33m    IOWeight: '[not set]'[0m
[0;33m    IOWriteBytes: '18446744073709551615'[0m
[0;33m    IOWriteOperations: '18446744073709551615'[0m
[0;33m    IPAccounting: 'no'[0m
[0;33m    IPEgressBytes: '[no data]'[0m
[0;33m    IPEgressPackets: '[no data]'[0m
[0;33m    IPIngressBytes: '[no data]'[0m
[0;33m    IPIngressPackets: '[no data]'[0m
[0;33m    Id: fail2ban.service[0m
[0;33m    IgnoreOnIsolate: 'no'[0m
[0;33m    IgnoreSIGPIPE: 'yes'[0m
[0;33m    InactiveEnterTimestamp: n/a[0m
[0;33m    InactiveEnterTimestampMonotonic: '0'[0m
[0;33m    InactiveExitTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    InactiveExitTimestampMonotonic: '*********'[0m
[0;33m    InvocationID: d2f69ee413624cd0ac7868b41444129c[0m
[0;33m    JobRunningTimeoutUSec: infinity[0m
[0;33m    JobTimeoutAction: none[0m
[0;33m    JobTimeoutUSec: infinity[0m
[0;33m    KeyringMode: private[0m
[0;33m    KillMode: control-group[0m
[0;33m    KillSignal: '15'[0m
[0;33m    LimitAS: infinity[0m
[0;33m    LimitASSoft: infinity[0m
[0;33m    LimitCORE: infinity[0m
[0;33m    LimitCORESoft: '0'[0m
[0;33m    LimitCPU: infinity[0m
[0;33m    LimitCPUSoft: infinity[0m
[0;33m    LimitDATA: infinity[0m
[0;33m    LimitDATASoft: infinity[0m
[0;33m    LimitFSIZE: infinity[0m
[0;33m    LimitFSIZESoft: infinity[0m
[0;33m    LimitLOCKS: infinity[0m
[0;33m    LimitLOCKSSoft: infinity[0m
[0;33m    LimitMEMLOCK: '8388608'[0m
[0;33m    LimitMEMLOCKSoft: '8388608'[0m
[0;33m    LimitMSGQUEUE: '819200'[0m
[0;33m    LimitMSGQUEUESoft: '819200'[0m
[0;33m    LimitNICE: '0'[0m
[0;33m    LimitNICESoft: '0'[0m
[0;33m    LimitNOFILE: '524288'[0m
[0;33m    LimitNOFILESoft: '1024'[0m
[0;33m    LimitNPROC: '3577'[0m
[0;33m    LimitNPROCSoft: '3577'[0m
[0;33m    LimitRSS: infinity[0m
[0;33m    LimitRSSSoft: infinity[0m
[0;33m    LimitRTPRIO: '0'[0m
[0;33m    LimitRTPRIOSoft: '0'[0m
[0;33m    LimitRTTIME: infinity[0m
[0;33m    LimitRTTIMESoft: infinity[0m
[0;33m    LimitSIGPENDING: '3577'[0m
[0;33m    LimitSIGPENDINGSoft: '3577'[0m
[0;33m    LimitSTACK: infinity[0m
[0;33m    LimitSTACKSoft: '8388608'[0m
[0;33m    LoadState: loaded[0m
[0;33m    LockPersonality: 'no'[0m
[0;33m    LogLevelMax: '-1'[0m
[0;33m    LogRateLimitBurst: '0'[0m
[0;33m    LogRateLimitIntervalUSec: '0'[0m
[0;33m    LogsDirectoryMode: '0755'[0m
[0;33m    MainPID: '9317'[0m
[0;33m    ManagedOOMMemoryPressure: auto[0m
[0;33m    ManagedOOMMemoryPressureLimit: '0'[0m
[0;33m    ManagedOOMPreference: none[0m
[0;33m    ManagedOOMSwap: auto[0m
[0;33m    MemoryAccounting: 'yes'[0m
[0;33m    MemoryAvailable: infinity[0m
[0;33m    MemoryCurrent: '********'[0m
[0;33m    MemoryDenyWriteExecute: 'no'[0m
[0;33m    MemoryHigh: infinity[0m
[0;33m    MemoryLimit: infinity[0m
[0;33m    MemoryLow: '0'[0m
[0;33m    MemoryMax: infinity[0m
[0;33m    MemoryMin: '0'[0m
[0;33m    MemorySwapMax: infinity[0m
[0;33m    MountAPIVFS: 'no'[0m
[0;33m    NFileDescriptorStore: '0'[0m
[0;33m    NRestarts: '0'[0m
[0;33m    NUMAPolicy: n/a[0m
[0;33m    Names: fail2ban.service[0m
[0;33m    NeedDaemonReload: 'no'[0m
[0;33m    Nice: '0'[0m
[0;33m    NoNewPrivileges: 'no'[0m
[0;33m    NonBlocking: 'no'[0m
[0;33m    NotifyAccess: none[0m
[0;33m    OOMPolicy: stop[0m
[0;33m    OOMScoreAdjust: '0'[0m
[0;33m    OnFailureJobMode: replace[0m
[0;33m    OnSuccessJobMode: fail[0m
[0;33m    PIDFile: /run/fail2ban/fail2ban.pid[0m
[0;33m    PartOf: firewalld.service[0m
[0;33m    Perpetual: 'no'[0m
[0;33m    PrivateDevices: 'no'[0m
[0;33m    PrivateIPC: 'no'[0m
[0;33m    PrivateMounts: 'no'[0m
[0;33m    PrivateNetwork: 'no'[0m
[0;33m    PrivateTmp: 'no'[0m
[0;33m    PrivateUsers: 'no'[0m
[0;33m    ProcSubset: all[0m
[0;33m    ProtectClock: 'no'[0m
[0;33m    ProtectControlGroups: 'no'[0m
[0;33m    ProtectHome: 'no'[0m
[0;33m    ProtectHostname: 'no'[0m
[0;33m    ProtectKernelLogs: 'no'[0m
[0;33m    ProtectKernelModules: 'no'[0m
[0;33m    ProtectKernelTunables: 'no'[0m
[0;33m    ProtectProc: default[0m
[0;33m    ProtectSystem: 'no'[0m
[0;33m    RefuseManualStart: 'no'[0m
[0;33m    RefuseManualStop: 'no'[0m
[0;33m    ReloadResult: success[0m
[0;33m    RemainAfterExit: 'no'[0m
[0;33m    RemoveIPC: 'no'[0m
[0;33m    Requires: sysinit.target -.mount system.slice[0m
[0;33m    RequiresMountsFor: /run/fail2ban[0m
[0;33m    Restart: on-failure[0m
[0;33m    RestartKillSignal: '15'[0m
[0;33m    RestartPreventExitStatus: 0 255[0m
[0;33m    RestartUSec: 100ms[0m
[0;33m    RestrictNamespaces: 'no'[0m
[0;33m    RestrictRealtime: 'no'[0m
[0;33m    RestrictSUIDSGID: 'no'[0m
[0;33m    Result: success[0m
[0;33m    RootDirectoryStartOnly: 'no'[0m
[0;33m    RuntimeDirectory: fail2ban[0m
[0;33m    RuntimeDirectoryMode: '0755'[0m
[0;33m    RuntimeDirectoryPreserve: 'no'[0m
[0;33m    RuntimeMaxUSec: infinity[0m
[0;33m    SameProcessGroup: 'no'[0m
[0;33m    SecureBits: '0'[0m
[0;33m    SendSIGHUP: 'no'[0m
[0;33m    SendSIGKILL: 'yes'[0m
[0;33m    Slice: system.slice[0m
[0;33m    StandardError: inherit[0m
[0;33m    StandardInput: 'null'[0m
[0;33m    StandardOutput: journal[0m
[0;33m    StartLimitAction: none[0m
[0;33m    StartLimitBurst: '5'[0m
[0;33m    StartLimitIntervalUSec: 10s[0m
[0;33m    StartupBlockIOWeight: '[not set]'[0m
[0;33m    StartupCPUShares: '[not set]'[0m
[0;33m    StartupCPUWeight: '[not set]'[0m
[0;33m    StartupIOWeight: '[not set]'[0m
[0;33m    StateChangeTimestamp: Thu 2025-08-21 11:31:23 UTC[0m
[0;33m    StateChangeTimestampMonotonic: '*********'[0m
[0;33m    StateDirectoryMode: '0755'[0m
[0;33m    StatusErrno: '0'[0m
[0;33m    StopWhenUnneeded: 'no'[0m
[0;33m    SubState: running[0m
[0;33m    SuccessAction: none[0m
[0;33m    SyslogFacility: '3'[0m
[0;33m    SyslogLevel: '6'[0m
[0;33m    SyslogLevelPrefix: 'yes'[0m
[0;33m    SyslogPriority: '30'[0m
[0;33m    SystemCallErrorNumber: '**********'[0m
[0;33m    TTYReset: 'no'[0m
[0;33m    TTYVHangup: 'no'[0m
[0;33m    TTYVTDisallocate: 'no'[0m
[0;33m    TasksAccounting: 'yes'[0m
[0;33m    TasksCurrent: '5'[0m
[0;33m    TasksMax: '1073'[0m
[0;33m    TimeoutAbortUSec: 1min 30s[0m
[0;33m    TimeoutCleanUSec: infinity[0m
[0;33m    TimeoutStartFailureMode: terminate[0m
[0;33m    TimeoutStartUSec: 1min 30s[0m
[0;33m    TimeoutStopFailureMode: terminate[0m
[0;33m    TimeoutStopUSec: 1min 30s[0m
[0;33m    TimerSlackNSec: '50000'[0m
[0;33m    Transient: 'no'[0m
[0;33m    Type: simple[0m
[0;33m    UID: '[not set]'[0m
[0;33m    UMask: '0022'[0m
[0;33m    UnitFilePreset: enabled[0m
[0;33m    UnitFileState: enabled[0m
[0;33m    UtmpMode: init[0m
[0;33m    WantedBy: multi-user.target[0m
[0;33m    WatchdogSignal: '6'[0m
[0;33m    WatchdogTimestamp: n/a[0m
[0;33m    WatchdogTimestampMonotonic: '0'[0m
[0;33m    WatchdogUSec: '0'[0m

PLAY RECAP *********************************************************************
[0;33mweb_server[0m                 : [0;32mok=20  [0m [0;33mchanged=16  [0m unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   

