Playbook: bootstrap.yml
Return code: 0
================================================================================
STDOUT:
[0;34mNo config file found; using defaults[0m

PLAY [Bootstrap Server Configuration] ******************************************

TASK [Gathering Facts] *********************************************************
[0;32mok: [web_server][0m

TASK [Update apt cache] ********************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755775719[0m
[0;33m  cache_updated: true[0m

TASK [Upgrade all packages] ****************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  msg: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    Calculating upgrade...[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      linux-aws-6.8-headers-6.8.0-1035 linux-aws-6.8-tools-6.8.0-1035[0m
[0;33m      linux-headers-6.8.0-1035-aws linux-image-6.8.0-1035-aws[0m
[0;33m      linux-modules-6.8.0-1035-aws linux-tools-6.8.0-1035-aws[0m
[0;33m    The following packages will be upgraded:[0m
[0;33m      apport gcc-12-base libgcc-s1 libglib2.0-0 libglib2.0-bin libglib2.0-data[0m
[0;33m      libstdc++6 libxml2 linux-aws linux-headers-aws linux-image-aws[0m
[0;33m      linux-tools-common powermgmt-base python3-apport python3-problem-report[0m
[0;33m      snapd[0m
[0;33m    16 upgraded, 6 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 94.6 MB of archives.[0m
[0;33m    After this operation, 293 MB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 gcc-12-base amd64 12.3.0-1ubuntu1~22.04.2 [20.6 kB][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libstdc++6 amd64 12.3.0-1ubuntu1~22.04.2 [699 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libgcc-s1 amd64 12.3.0-1ubuntu1~22.04.2 [53.9 kB][0m
[0;33m    Get:4 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-data all 2.72.4-0ubuntu2.6 [4698 B][0m
[0;33m    Get:5 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-bin amd64 2.72.4-0ubuntu2.6 [80.9 kB][0m
[0;33m    Get:6 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-0 amd64 2.72.4-0ubuntu2.6 [1467 kB][0m
[0;33m    Get:7 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libxml2 amd64 2.9.13+dfsg-1ubuntu0.8 [763 kB][0m
[0;33m    Get:8 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 powermgmt-base all 1.36ubuntu0.22.04.1 [7736 B][0m
[0;33m    Get:9 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-problem-report all 2.20.11-0ubuntu82.10 [11.4 kB][0m
[0;33m    Get:10 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-apport all 2.20.11-0ubuntu82.10 [89.0 kB][0m
[0;33m    Get:11 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 apport all 2.20.11-0ubuntu82.10 [135 kB][0m
[0;33m    Get:12 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-modules-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [25.4 MB][0m
[0;33m    Get:13 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-image-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [14.8 MB][0m
[0;33m    Get:14 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws amd64 6.8.0-1035.37~22.04.1 [1726 B][0m
[0;33m    Get:15 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-image-aws amd64 6.8.0-1035.37~22.04.1 [2658 B][0m
[0;33m    Get:16 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws-6.8-headers-6.8.0-1035 all 6.8.0-1035.37~22.04.1 [13.6 MB][0m
[0;33m    Get:17 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-headers-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [3591 kB][0m
[0;33m    Get:18 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-headers-aws amd64 6.8.0-1035.37~22.04.1 [2572 B][0m
[0;33m    Get:19 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-tools-common all 5.15.0-152.162 [294 kB][0m
[0;33m    Get:20 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws-6.8-tools-6.8.0-1035 amd64 6.8.0-1035.37~22.04.1 [3655 kB][0m
[0;33m    Get:21 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-tools-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [1798 B][0m
[0;33m    Get:22 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 snapd amd64 2.68.5+ubuntu22.04.1 [30.0 MB][0m
[0;33m    Fetched 94.6 MB in 1s (83.3 MB/s)[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../gcc-12-base_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking gcc-12-base:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up gcc-12-base:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../libstdc++6_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libstdc++6:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up libstdc++6:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../libgcc-s1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libgcc-s1:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up libgcc-s1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../00-libglib2.0-data_2.72.4-0ubuntu2.6_all.deb ...[0m
[0;33m    Unpacking libglib2.0-data (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../01-libglib2.0-bin_2.72.4-0ubuntu2.6_amd64.deb ...[0m
[0;33m    Unpacking libglib2.0-bin (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../02-libglib2.0-0_2.72.4-0ubuntu2.6_amd64.deb ...[0m
[0;33m    Unpacking libglib2.0-0:amd64 (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../03-libxml2_2.9.13+dfsg-1ubuntu0.8_amd64.deb ...[0m
[0;33m    Unpacking libxml2:amd64 (2.9.13+dfsg-1ubuntu0.8) over (2.9.13+dfsg-1ubuntu0.7) ...[0m
[0;33m    Preparing to unpack .../04-powermgmt-base_1.36ubuntu0.22.04.1_all.deb ...[0m
[0;33m    Unpacking powermgmt-base (1.36ubuntu0.22.04.1) over (1.36) ...[0m
[0;33m    Preparing to unpack .../05-python3-problem-report_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking python3-problem-report (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Preparing to unpack .../06-python3-apport_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking python3-apport (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Preparing to unpack .../07-apport_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking apport (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Selecting previously unselected package linux-modules-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../08-linux-modules-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-modules-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-image-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../09-linux-image-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../10-linux-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Preparing to unpack .../11-linux-image-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-image-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-aws-6.8-headers-6.8.0-1035.[0m
[0;33m    Preparing to unpack .../12-linux-aws-6.8-headers-6.8.0-1035_6.8.0-1035.37~22.04.1_all.deb ...[0m
[0;33m    Unpacking linux-aws-6.8-headers-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-headers-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../13-linux-headers-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-headers-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../14-linux-headers-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-headers-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Preparing to unpack .../15-linux-tools-common_5.15.0-152.162_all.deb ...[0m
[0;33m    Unpacking linux-tools-common (5.15.0-152.162) over (5.15.0-151.161) ...[0m
[0;33m    Selecting previously unselected package linux-aws-6.8-tools-6.8.0-1035.[0m
[0;33m    Preparing to unpack .../16-linux-aws-6.8-tools-6.8.0-1035_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-aws-6.8-tools-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-tools-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../17-linux-tools-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-tools-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../18-snapd_2.68.5+ubuntu22.04.1_amd64.deb ...[0m
[0;33m    Unpacking snapd (2.68.5+ubuntu22.04.1) over (2.67.1+22.04) ...[0m
[0;33m    Setting up powermgmt-base (1.36ubuntu0.22.04.1) ...[0m
[0;33m    Setting up snapd (2.68.5+ubuntu22.04.1) ...[0m
[0;33m    snapd.failure.service is a disabled or a static unit not running, not starting it.[0m
[0;33m    snapd.snap-repair.service is a disabled or a static unit not running, not starting it.[0m
[0;33m    Setting up python3-problem-report (2.20.11-0ubuntu82.10) ...[0m
[0;33m    Setting up libglib2.0-0:amd64 (2.72.4-0ubuntu2.6) ...[0m
[0;33m    No schema files found: doing nothing.[0m
[0;33m    Setting up linux-aws-6.8-headers-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up python3-apport (2.20.11-0ubuntu82.10) ...[0m
[0;33m    Setting up libglib2.0-data (2.72.4-0ubuntu2.6) ...[0m
[0;33m    Setting up linux-headers-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-tools-common (5.15.0-152.162) ...[0m
[0;33m    Setting up libxml2:amd64 (2.9.13+dfsg-1ubuntu0.8) ...[0m
[0;33m    Setting up linux-headers-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up libglib2.0-bin (2.72.4-0ubuntu2.6) ...[0m
[0;33m    Setting up linux-aws-6.8-tools-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up apport (2.20.11-0ubuntu82.10) ...[0m
[0;33m    apport-autoreport.service is a disabled or a static unit, not starting it.[0m
[0;33m    Setting up linux-tools-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    I: /boot/vmlinuz is now a symlink to vmlinuz-6.8.0-1035-aws[0m
[0;33m    I: /boot/initrd.img is now a symlink to initrd.img-6.8.0-1035-aws[0m
[0;33m    Setting up linux-modules-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-image-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    Processing triggers for dbus (1.12.20-2ubuntu4.1) ...[0m
[0;33m    Processing triggers for libc-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    Processing triggers for linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    /etc/kernel/postinst.d/initramfs-tools:[0m
[0;33m    update-initramfs: Generating /boot/initrd.img-6.8.0-1035-aws[0m
[0;33m    /etc/kernel/postinst.d/zz-update-grub:[0m
[0;33m    Sourcing file `/etc/default/grub'[0m
[0;33m    Sourcing file `/etc/default/grub.d/40-force-partuuid.cfg'[0m
[0;33m    Sourcing file `/etc/default/grub.d/50-cloudimg-settings.cfg'[0m
[0;33m    Sourcing file `/etc/default/grub.d/init-select.cfg'[0m
[0;33m    Generating grub configuration file ...[0m
[0;33m    GRUB_FORCE_PARTUUID is set, will attempt initrdless boot[0m
[0;33m    Found linux image: /boot/vmlinuz-6.8.0-1035-aws[0m
[0;33m    Found initrd image: /boot/microcode.cpio /boot/initrd.img-6.8.0-1035-aws[0m
[0;33m    Found linux image: /boot/vmlinuz-6.8.0-1033-aws[0m
[0;33m    Found initrd image: /boot/microcode.cpio /boot/initrd.img-6.8.0-1033-aws[0m
[0;33m    Warning: os-prober will not be executed to detect other bootable partitions.[0m
[0;33m    Systems on them will not be added to the GRUB boot configuration.[0m
[0;33m    Check GRUB_DISABLE_OS_PROBER documentation entry.[0m
[0;33m    Adding boot menu entry for UEFI Firmware Settings ...[0m
[0;33m    done[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    Calculating upgrade...[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      linux-aws-6.8-headers-6.8.0-1035 linux-aws-6.8-tools-6.8.0-1035[0m
[0;33m      linux-headers-6.8.0-1035-aws linux-image-6.8.0-1035-aws[0m
[0;33m      linux-modules-6.8.0-1035-aws linux-tools-6.8.0-1035-aws[0m
[0;33m    The following packages will be upgraded:[0m
[0;33m      apport gcc-12-base libgcc-s1 libglib2.0-0 libglib2.0-bin libglib2.0-data[0m
[0;33m      libstdc++6 libxml2 linux-aws linux-headers-aws linux-image-aws[0m
[0;33m      linux-tools-common powermgmt-base python3-apport python3-problem-report[0m
[0;33m      snapd[0m
[0;33m    16 upgraded, 6 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 94.6 MB of archives.[0m
[0;33m    After this operation, 293 MB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 gcc-12-base amd64 12.3.0-1ubuntu1~22.04.2 [20.6 kB][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libstdc++6 amd64 12.3.0-1ubuntu1~22.04.2 [699 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libgcc-s1 amd64 12.3.0-1ubuntu1~22.04.2 [53.9 kB][0m
[0;33m    Get:4 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-data all 2.72.4-0ubuntu2.6 [4698 B][0m
[0;33m    Get:5 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-bin amd64 2.72.4-0ubuntu2.6 [80.9 kB][0m
[0;33m    Get:6 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libglib2.0-0 amd64 2.72.4-0ubuntu2.6 [1467 kB][0m
[0;33m    Get:7 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libxml2 amd64 2.9.13+dfsg-1ubuntu0.8 [763 kB][0m
[0;33m    Get:8 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 powermgmt-base all 1.36ubuntu0.22.04.1 [7736 B][0m
[0;33m    Get:9 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-problem-report all 2.20.11-0ubuntu82.10 [11.4 kB][0m
[0;33m    Get:10 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-apport all 2.20.11-0ubuntu82.10 [89.0 kB][0m
[0;33m    Get:11 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 apport all 2.20.11-0ubuntu82.10 [135 kB][0m
[0;33m    Get:12 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-modules-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [25.4 MB][0m
[0;33m    Get:13 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-image-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [14.8 MB][0m
[0;33m    Get:14 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws amd64 6.8.0-1035.37~22.04.1 [1726 B][0m
[0;33m    Get:15 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-image-aws amd64 6.8.0-1035.37~22.04.1 [2658 B][0m
[0;33m    Get:16 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws-6.8-headers-6.8.0-1035 all 6.8.0-1035.37~22.04.1 [13.6 MB][0m
[0;33m    Get:17 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-headers-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [3591 kB][0m
[0;33m    Get:18 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-headers-aws amd64 6.8.0-1035.37~22.04.1 [2572 B][0m
[0;33m    Get:19 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-tools-common all 5.15.0-152.162 [294 kB][0m
[0;33m    Get:20 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-aws-6.8-tools-6.8.0-1035 amd64 6.8.0-1035.37~22.04.1 [3655 kB][0m
[0;33m    Get:21 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-tools-6.8.0-1035-aws amd64 6.8.0-1035.37~22.04.1 [1798 B][0m
[0;33m    Get:22 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 snapd amd64 2.68.5+ubuntu22.04.1 [30.0 MB][0m
[0;33m    Fetched 94.6 MB in 1s (83.3 MB/s)[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../gcc-12-base_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking gcc-12-base:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up gcc-12-base:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../libstdc++6_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libstdc++6:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up libstdc++6:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../libgcc-s1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libgcc-s1:amd64 (12.3.0-1ubuntu1~22.04.2) over (12.3.0-1ubuntu1~22.04) ...[0m
[0;33m    Setting up libgcc-s1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 65985 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../00-libglib2.0-data_2.72.4-0ubuntu2.6_all.deb ...[0m
[0;33m    Unpacking libglib2.0-data (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../01-libglib2.0-bin_2.72.4-0ubuntu2.6_amd64.deb ...[0m
[0;33m    Unpacking libglib2.0-bin (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../02-libglib2.0-0_2.72.4-0ubuntu2.6_amd64.deb ...[0m
[0;33m    Unpacking libglib2.0-0:amd64 (2.72.4-0ubuntu2.6) over (2.72.4-0ubuntu2.5) ...[0m
[0;33m    Preparing to unpack .../03-libxml2_2.9.13+dfsg-1ubuntu0.8_amd64.deb ...[0m
[0;33m    Unpacking libxml2:amd64 (2.9.13+dfsg-1ubuntu0.8) over (2.9.13+dfsg-1ubuntu0.7) ...[0m
[0;33m    Preparing to unpack .../04-powermgmt-base_1.36ubuntu0.22.04.1_all.deb ...[0m
[0;33m    Unpacking powermgmt-base (1.36ubuntu0.22.04.1) over (1.36) ...[0m
[0;33m    Preparing to unpack .../05-python3-problem-report_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking python3-problem-report (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Preparing to unpack .../06-python3-apport_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking python3-apport (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Preparing to unpack .../07-apport_2.20.11-0ubuntu82.10_all.deb ...[0m
[0;33m    Unpacking apport (2.20.11-0ubuntu82.10) over (2.20.11-0ubuntu82.9) ...[0m
[0;33m    Selecting previously unselected package linux-modules-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../08-linux-modules-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-modules-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-image-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../09-linux-image-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../10-linux-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Preparing to unpack .../11-linux-image-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-image-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-aws-6.8-headers-6.8.0-1035.[0m
[0;33m    Preparing to unpack .../12-linux-aws-6.8-headers-6.8.0-1035_6.8.0-1035.37~22.04.1_all.deb ...[0m
[0;33m    Unpacking linux-aws-6.8-headers-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-headers-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../13-linux-headers-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-headers-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../14-linux-headers-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-headers-aws (6.8.0-1035.37~22.04.1) over (6.8.0-1033.35~22.04.1) ...[0m
[0;33m    Preparing to unpack .../15-linux-tools-common_5.15.0-152.162_all.deb ...[0m
[0;33m    Unpacking linux-tools-common (5.15.0-152.162) over (5.15.0-151.161) ...[0m
[0;33m    Selecting previously unselected package linux-aws-6.8-tools-6.8.0-1035.[0m
[0;33m    Preparing to unpack .../16-linux-aws-6.8-tools-6.8.0-1035_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-aws-6.8-tools-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Selecting previously unselected package linux-tools-6.8.0-1035-aws.[0m
[0;33m    Preparing to unpack .../17-linux-tools-6.8.0-1035-aws_6.8.0-1035.37~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking linux-tools-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Preparing to unpack .../18-snapd_2.68.5+ubuntu22.04.1_amd64.deb ...[0m
[0;33m    Unpacking snapd (2.68.5+ubuntu22.04.1) over (2.67.1+22.04) ...[0m
[0;33m    Setting up powermgmt-base (1.36ubuntu0.22.04.1) ...[0m
[0;33m    Setting up snapd (2.68.5+ubuntu22.04.1) ...[0m
[0;33m    snapd.failure.service is a disabled or a static unit not running, not starting it.[0m
[0;33m    snapd.snap-repair.service is a disabled or a static unit not running, not starting it.[0m
[0;33m    Setting up python3-problem-report (2.20.11-0ubuntu82.10) ...[0m
[0;33m    Setting up libglib2.0-0:amd64 (2.72.4-0ubuntu2.6) ...[0m
[0;33m    No schema files found: doing nothing.[0m
[0;33m    Setting up linux-aws-6.8-headers-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up python3-apport (2.20.11-0ubuntu82.10) ...[0m
[0;33m    Setting up libglib2.0-data (2.72.4-0ubuntu2.6) ...[0m
[0;33m    Setting up linux-headers-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-tools-common (5.15.0-152.162) ...[0m
[0;33m    Setting up libxml2:amd64 (2.9.13+dfsg-1ubuntu0.8) ...[0m
[0;33m    Setting up linux-headers-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up libglib2.0-bin (2.72.4-0ubuntu2.6) ...[0m
[0;33m    Setting up linux-aws-6.8-tools-6.8.0-1035 (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up apport (2.20.11-0ubuntu82.10) ...[0m
[0;33m    apport-autoreport.service is a disabled or a static unit, not starting it.[0m
[0;33m    Setting up linux-tools-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    I: /boot/vmlinuz is now a symlink to vmlinuz-6.8.0-1035-aws[0m
[0;33m    I: /boot/initrd.img is now a symlink to initrd.img-6.8.0-1035-aws[0m
[0;33m    Setting up linux-modules-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-image-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Setting up linux-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    Processing triggers for dbus (1.12.20-2ubuntu4.1) ...[0m
[0;33m    Processing triggers for libc-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    Processing triggers for linux-image-6.8.0-1035-aws (6.8.0-1035.37~22.04.1) ...[0m
[0;33m    /etc/kernel/postinst.d/initramfs-tools:[0m
[0;33m    update-initramfs: Generating /boot/initrd.img-6.8.0-1035-aws[0m
[0;33m    /etc/kernel/postinst.d/zz-update-grub:[0m
[0;33m    Sourcing file `/etc/default/grub'[0m
[0;33m    Sourcing file `/etc/default/grub.d/40-force-partuuid.cfg'[0m
[0;33m    Sourcing file `/etc/default/grub.d/50-cloudimg-settings.cfg'[0m
[0;33m    Sourcing file `/etc/default/grub.d/init-select.cfg'[0m
[0;33m    Generating grub configuration file ...[0m
[0;33m    GRUB_FORCE_PARTUUID is set, will attempt initrdless boot[0m
[0;33m    Found linux image: /boot/vmlinuz-6.8.0-1035-aws[0m
[0;33m    Found initrd image: /boot/microcode.cpio /boot/initrd.img-6.8.0-1035-aws[0m
[0;33m    Found linux image: /boot/vmlinuz-6.8.0-1033-aws[0m
[0;33m    Found initrd image: /boot/microcode.cpio /boot/initrd.img-6.8.0-1033-aws[0m
[0;33m    Warning: os-prober will not be executed to detect other bootable partitions.[0m
[0;33m    Systems on them will not be added to the GRUB boot configuration.[0m
[0;33m    Check GRUB_DISABLE_OS_PROBER documentation entry.[0m
[0;33m    Adding boot menu entry for UEFI Firmware Settings ...[0m
[0;33m    done[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Install essential packages] **********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755775719[0m
[0;33m  cache_updated: false[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following additional packages will be installed:[0m
[0;33m      bzip2 cpp cpp-11 dpkg-dev fakeroot fontconfig-config fonts-dejavu-core g++[0m
[0;33m      g++-11 gcc gcc-11 gcc-11-base libalgorithm-diff-perl[0m
[0;33m      libalgorithm-diff-xs-perl libalgorithm-merge-perl libasan6 libatomic1[0m
[0;33m      libc-dev-bin libc-devtools libc6-dev libcc1-0 libcrypt-dev libdeflate0[0m
[0;33m      libdpkg-perl libfakeroot libfile-fcntllock-perl libfontconfig1 libgcc-11-dev[0m
[0;33m      libgd3 libgomp1 libisl23 libitm1 libjbig0 libjpeg-turbo8 libjpeg8 liblsan0[0m
[0;33m      libmpc3 libnsl-dev libquadmath0 libstdc++-11-dev libtiff5 libtirpc-dev[0m
[0;33m      libtsan0 libubsan1 libwebp7 libxpm4 linux-libc-dev lto-disabled-list make[0m
[0;33m      manpages-dev rpcsvc-proto[0m
[0;33m    Suggested packages:[0m
[0;33m      bzip2-doc cpp-doc gcc-11-locales debian-keyring g++-multilib g++-11-multilib[0m
[0;33m      gcc-11-doc gcc-multilib autoconf automake libtool flex bison gdb gcc-doc[0m
[0;33m      gcc-11-multilib glibc-doc bzr libgd-tools libstdc++-11-doc make-doc zip[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      apt-transport-https build-essential bzip2 cpp cpp-11 dpkg-dev fakeroot[0m
[0;33m      fontconfig-config fonts-dejavu-core g++ g++-11 gcc gcc-11 gcc-11-base[0m
[0;33m      libalgorithm-diff-perl libalgorithm-diff-xs-perl libalgorithm-merge-perl[0m
[0;33m      libasan6 libatomic1 libc-dev-bin libc-devtools libc6-dev libcc1-0[0m
[0;33m      libcrypt-dev libdeflate0 libdpkg-perl libfakeroot libfile-fcntllock-perl[0m
[0;33m      libfontconfig1 libgcc-11-dev libgd3 libgomp1 libisl23 libitm1 libjbig0[0m
[0;33m      libjpeg-turbo8 libjpeg8 liblsan0 libmpc3 libnsl-dev libquadmath0[0m
[0;33m      libstdc++-11-dev libtiff5 libtirpc-dev libtsan0 libubsan1 libwebp7 libxpm4[0m
[0;33m      linux-libc-dev lto-disabled-list make manpages-dev rpcsvc-proto tree unzip[0m
[0;33m    0 upgraded, 55 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 64.0 MB of archives.[0m
[0;33m    After this operation, 208 MB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 apt-transport-https all 2.4.14 [1510 B][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libc-dev-bin amd64 2.35-0ubuntu3.10 [20.3 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 linux-libc-dev amd64 5.15.0-152.162 [1323 kB][0m
[0;33m    Get:4 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libcrypt-dev amd64 1:4.4.27-1 [112 kB][0m
[0;33m    Get:5 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 rpcsvc-proto amd64 1.4.2-0ubuntu6 [68.5 kB][0m
[0;33m    Get:6 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libtirpc-dev amd64 1.3.2-2ubuntu0.1 [192 kB][0m
[0;33m    Get:7 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libnsl-dev amd64 1.3.0-2build2 [71.3 kB][0m
[0;33m    Get:8 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libc6-dev amd64 2.35-0ubuntu3.10 [2100 kB][0m
[0;33m    Get:9 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 gcc-11-base amd64 11.4.0-1ubuntu1~22.04.2 [20.8 kB][0m
[0;33m    Get:10 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libisl23 amd64 0.24-2build1 [727 kB][0m
[0;33m    Get:11 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libmpc3 amd64 1.2.1-2build1 [46.9 kB][0m
[0;33m    Get:12 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 cpp-11 amd64 11.4.0-1ubuntu1~22.04.2 [10.0 MB][0m
[0;33m    Get:13 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 cpp amd64 4:11.2.0-1ubuntu1 [27.7 kB][0m
[0;33m    Get:14 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libcc1-0 amd64 12.3.0-1ubuntu1~22.04.2 [48.3 kB][0m
[0;33m    Get:15 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libgomp1 amd64 12.3.0-1ubuntu1~22.04.2 [127 kB][0m
[0;33m    Get:16 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libitm1 amd64 12.3.0-1ubuntu1~22.04.2 [30.2 kB][0m
[0;33m    Get:17 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libatomic1 amd64 12.3.0-1ubuntu1~22.04.2 [10.4 kB][0m
[0;33m    Get:18 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libasan6 amd64 11.4.0-1ubuntu1~22.04.2 [2283 kB][0m
[0;33m    Get:19 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 liblsan0 amd64 12.3.0-1ubuntu1~22.04.2 [1069 kB][0m
[0;33m    Get:20 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libtsan0 amd64 11.4.0-1ubuntu1~22.04.2 [2262 kB][0m
[0;33m    Get:21 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libubsan1 amd64 12.3.0-1ubuntu1~22.04.2 [976 kB][0m
[0;33m    Get:22 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libquadmath0 amd64 12.3.0-1ubuntu1~22.04.2 [154 kB][0m
[0;33m    Get:23 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libgcc-11-dev amd64 11.4.0-1ubuntu1~22.04.2 [2517 kB][0m
[0;33m    Get:24 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 gcc-11 amd64 11.4.0-1ubuntu1~22.04.2 [20.1 MB][0m
[0;33m    Get:25 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 gcc amd64 4:11.2.0-1ubuntu1 [5112 B][0m
[0;33m    Get:26 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libstdc++-11-dev amd64 11.4.0-1ubuntu1~22.04.2 [2101 kB][0m
[0;33m    Get:27 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 g++-11 amd64 11.4.0-1ubuntu1~22.04.2 [11.4 MB][0m
[0;33m    Get:28 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 g++ amd64 4:11.2.0-1ubuntu1 [1412 B][0m
[0;33m    Get:29 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 make amd64 4.3-4.1build1 [180 kB][0m
[0;33m    Get:30 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libdpkg-perl all 1.21.1ubuntu2.3 [237 kB][0m
[0;33m    Get:31 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 bzip2 amd64 1.0.8-5build1 [34.8 kB][0m
[0;33m    Get:32 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 lto-disabled-list all 24 [12.5 kB][0m
[0;33m    Get:33 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 dpkg-dev all 1.21.1ubuntu2.3 [922 kB][0m
[0;33m    Get:34 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 build-essential amd64 12.9ubuntu3 [4744 B][0m
[0;33m    Get:35 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libfakeroot amd64 1.28-1ubuntu1 [31.5 kB][0m
[0;33m    Get:36 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 fakeroot amd64 1.28-1ubuntu1 [60.4 kB][0m
[0;33m    Get:37 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 fonts-dejavu-core all 2.37-2build1 [1041 kB][0m
[0;33m    Get:38 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 fontconfig-config all 2.13.1-4.2ubuntu5 [29.1 kB][0m
[0;33m    Get:39 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libalgorithm-diff-perl all 1.201-1 [41.8 kB][0m
[0;33m    Get:40 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libalgorithm-diff-xs-perl amd64 0.04-6build3 [11.9 kB][0m
[0;33m    Get:41 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libalgorithm-merge-perl all 0.08-3 [12.0 kB][0m
[0;33m    Get:42 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libfontconfig1 amd64 2.13.1-4.2ubuntu5 [131 kB][0m
[0;33m    Get:43 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libjpeg-turbo8 amd64 2.1.2-0ubuntu1 [134 kB][0m
[0;33m    Get:44 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libjpeg8 amd64 8c-2ubuntu10 [2264 B][0m
[0;33m    Get:45 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libdeflate0 amd64 1.10-2 [70.9 kB][0m
[0;33m    Get:46 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libjbig0 amd64 2.1-3.1ubuntu0.22.04.1 [29.2 kB][0m
[0;33m    Get:47 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libwebp7 amd64 1.2.2-2ubuntu0.22.04.2 [206 kB][0m
[0;33m    Get:48 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libtiff5 amd64 4.3.0-6ubuntu0.11 [185 kB][0m
[0;33m    Get:49 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libxpm4 amd64 1:3.5.12-1ubuntu0.22.04.2 [36.7 kB][0m
[0;33m    Get:50 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libgd3 amd64 2.3.0-2ubuntu2.3 [129 kB][0m
[0;33m    Get:51 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libc-devtools amd64 2.35-0ubuntu3.10 [29.0 kB][0m
[0;33m    Get:52 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libfile-fcntllock-perl amd64 0.22-3build7 [33.9 kB][0m
[0;33m    Get:53 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 manpages-dev all 5.10-1ubuntu1 [2309 kB][0m
[0;33m    Get:54 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/universe amd64 tree amd64 2.0.2-1 [47.9 kB][0m
[0;33m    Get:55 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 unzip amd64 6.0-26ubuntu3.2 [175 kB][0m
[0;33m    Fetched 64.0 MB in 1s (86.3 MB/s)[0m
[0;33m    Selecting previously unselected package apt-transport-https.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 96585 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../00-apt-transport-https_2.4.14_all.deb ...[0m
[0;33m    Unpacking apt-transport-https (2.4.14) ...[0m
[0;33m    Selecting previously unselected package libc-dev-bin.[0m
[0;33m    Preparing to unpack .../01-libc-dev-bin_2.35-0ubuntu3.10_amd64.deb ...[0m
[0;33m    Unpacking libc-dev-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    Selecting previously unselected package linux-libc-dev:amd64.[0m
[0;33m    Preparing to unpack .../02-linux-libc-dev_5.15.0-152.162_amd64.deb ...[0m
[0;33m    Unpacking linux-libc-dev:amd64 (5.15.0-152.162) ...[0m
[0;33m    Selecting previously unselected package libcrypt-dev:amd64.[0m
[0;33m    Preparing to unpack .../03-libcrypt-dev_1%3a4.4.27-1_amd64.deb ...[0m
[0;33m    Unpacking libcrypt-dev:amd64 (1:4.4.27-1) ...[0m
[0;33m    Selecting previously unselected package rpcsvc-proto.[0m
[0;33m    Preparing to unpack .../04-rpcsvc-proto_1.4.2-0ubuntu6_amd64.deb ...[0m
[0;33m    Unpacking rpcsvc-proto (1.4.2-0ubuntu6) ...[0m
[0;33m    Selecting previously unselected package libtirpc-dev:amd64.[0m
[0;33m    Preparing to unpack .../05-libtirpc-dev_1.3.2-2ubuntu0.1_amd64.deb ...[0m
[0;33m    Unpacking libtirpc-dev:amd64 (1.3.2-2ubuntu0.1) ...[0m
[0;33m    Selecting previously unselected package libnsl-dev:amd64.[0m
[0;33m    Preparing to unpack .../06-libnsl-dev_1.3.0-2build2_amd64.deb ...[0m
[0;33m    Unpacking libnsl-dev:amd64 (1.3.0-2build2) ...[0m
[0;33m    Selecting previously unselected package libc6-dev:amd64.[0m
[0;33m    Preparing to unpack .../07-libc6-dev_2.35-0ubuntu3.10_amd64.deb ...[0m
[0;33m    Unpacking libc6-dev:amd64 (2.35-0ubuntu3.10) ...[0m
[0;33m    Selecting previously unselected package gcc-11-base:amd64.[0m
[0;33m    Preparing to unpack .../08-gcc-11-base_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking gcc-11-base:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libisl23:amd64.[0m
[0;33m    Preparing to unpack .../09-libisl23_0.24-2build1_amd64.deb ...[0m
[0;33m    Unpacking libisl23:amd64 (0.24-2build1) ...[0m
[0;33m    Selecting previously unselected package libmpc3:amd64.[0m
[0;33m    Preparing to unpack .../10-libmpc3_1.2.1-2build1_amd64.deb ...[0m
[0;33m    Unpacking libmpc3:amd64 (1.2.1-2build1) ...[0m
[0;33m    Selecting previously unselected package cpp-11.[0m
[0;33m    Preparing to unpack .../11-cpp-11_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking cpp-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package cpp.[0m
[0;33m    Preparing to unpack .../12-cpp_4%3a11.2.0-1ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking cpp (4:11.2.0-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package libcc1-0:amd64.[0m
[0;33m    Preparing to unpack .../13-libcc1-0_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libcc1-0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libgomp1:amd64.[0m
[0;33m    Preparing to unpack .../14-libgomp1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libgomp1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libitm1:amd64.[0m
[0;33m    Preparing to unpack .../15-libitm1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libitm1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libatomic1:amd64.[0m
[0;33m    Preparing to unpack .../16-libatomic1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libatomic1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libasan6:amd64.[0m
[0;33m    Preparing to unpack .../17-libasan6_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libasan6:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package liblsan0:amd64.[0m
[0;33m    Preparing to unpack .../18-liblsan0_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking liblsan0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libtsan0:amd64.[0m
[0;33m    Preparing to unpack .../19-libtsan0_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libtsan0:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libubsan1:amd64.[0m
[0;33m    Preparing to unpack .../20-libubsan1_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libubsan1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libquadmath0:amd64.[0m
[0;33m    Preparing to unpack .../21-libquadmath0_12.3.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libquadmath0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package libgcc-11-dev:amd64.[0m
[0;33m    Preparing to unpack .../22-libgcc-11-dev_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libgcc-11-dev:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package gcc-11.[0m
[0;33m    Preparing to unpack .../23-gcc-11_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking gcc-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package gcc.[0m
[0;33m    Preparing to unpack .../24-gcc_4%3a11.2.0-1ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking gcc (4:11.2.0-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package libstdc++-11-dev:amd64.[0m
[0;33m    Preparing to unpack .../25-libstdc++-11-dev_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libstdc++-11-dev:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package g++-11.[0m
[0;33m    Preparing to unpack .../26-g++-11_11.4.0-1ubuntu1~22.04.2_amd64.deb ...[0m
[0;33m    Unpacking g++-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Selecting previously unselected package g++.[0m
[0;33m    Preparing to unpack .../27-g++_4%3a11.2.0-1ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking g++ (4:11.2.0-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package make.[0m
[0;33m    Preparing to unpack .../28-make_4.3-4.1build1_amd64.deb ...[0m
[0;33m    Unpacking make (4.3-4.1build1) ...[0m
[0;33m    Selecting previously unselected package libdpkg-perl.[0m
[0;33m    Preparing to unpack .../29-libdpkg-perl_1.21.1ubuntu2.3_all.deb ...[0m
[0;33m    Unpacking libdpkg-perl (1.21.1ubuntu2.3) ...[0m
[0;33m    Selecting previously unselected package bzip2.[0m
[0;33m    Preparing to unpack .../30-bzip2_1.0.8-5build1_amd64.deb ...[0m
[0;33m    Unpacking bzip2 (1.0.8-5build1) ...[0m
[0;33m    Selecting previously unselected package lto-disabled-list.[0m
[0;33m    Preparing to unpack .../31-lto-disabled-list_24_all.deb ...[0m
[0;33m    Unpacking lto-disabled-list (24) ...[0m
[0;33m    Selecting previously unselected package dpkg-dev.[0m
[0;33m    Preparing to unpack .../32-dpkg-dev_1.21.1ubuntu2.3_all.deb ...[0m
[0;33m    Unpacking dpkg-dev (1.21.1ubuntu2.3) ...[0m
[0;33m    Selecting previously unselected package build-essential.[0m
[0;33m    Preparing to unpack .../33-build-essential_12.9ubuntu3_amd64.deb ...[0m
[0;33m    Unpacking build-essential (12.9ubuntu3) ...[0m
[0;33m    Selecting previously unselected package libfakeroot:amd64.[0m
[0;33m    Preparing to unpack .../34-libfakeroot_1.28-1ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking libfakeroot:amd64 (1.28-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package fakeroot.[0m
[0;33m    Preparing to unpack .../35-fakeroot_1.28-1ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking fakeroot (1.28-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package fonts-dejavu-core.[0m
[0;33m    Preparing to unpack .../36-fonts-dejavu-core_2.37-2build1_all.deb ...[0m
[0;33m    Unpacking fonts-dejavu-core (2.37-2build1) ...[0m
[0;33m    Selecting previously unselected package fontconfig-config.[0m
[0;33m    Preparing to unpack .../37-fontconfig-config_2.13.1-4.2ubuntu5_all.deb ...[0m
[0;33m    Unpacking fontconfig-config (2.13.1-4.2ubuntu5) ...[0m
[0;33m    Selecting previously unselected package libalgorithm-diff-perl.[0m
[0;33m    Preparing to unpack .../38-libalgorithm-diff-perl_1.201-1_all.deb ...[0m
[0;33m    Unpacking libalgorithm-diff-perl (1.201-1) ...[0m
[0;33m    Selecting previously unselected package libalgorithm-diff-xs-perl.[0m
[0;33m    Preparing to unpack .../39-libalgorithm-diff-xs-perl_0.04-6build3_amd64.deb ...[0m
[0;33m    Unpacking libalgorithm-diff-xs-perl (0.04-6build3) ...[0m
[0;33m    Selecting previously unselected package libalgorithm-merge-perl.[0m
[0;33m    Preparing to unpack .../40-libalgorithm-merge-perl_0.08-3_all.deb ...[0m
[0;33m    Unpacking libalgorithm-merge-perl (0.08-3) ...[0m
[0;33m    Selecting previously unselected package libfontconfig1:amd64.[0m
[0;33m    Preparing to unpack .../41-libfontconfig1_2.13.1-4.2ubuntu5_amd64.deb ...[0m
[0;33m    Unpacking libfontconfig1:amd64 (2.13.1-4.2ubuntu5) ...[0m
[0;33m    Selecting previously unselected package libjpeg-turbo8:amd64.[0m
[0;33m    Preparing to unpack .../42-libjpeg-turbo8_2.1.2-0ubuntu1_amd64.deb ...[0m
[0;33m    Unpacking libjpeg-turbo8:amd64 (2.1.2-0ubuntu1) ...[0m
[0;33m    Selecting previously unselected package libjpeg8:amd64.[0m
[0;33m    Preparing to unpack .../43-libjpeg8_8c-2ubuntu10_amd64.deb ...[0m
[0;33m    Unpacking libjpeg8:amd64 (8c-2ubuntu10) ...[0m
[0;33m    Selecting previously unselected package libdeflate0:amd64.[0m
[0;33m    Preparing to unpack .../44-libdeflate0_1.10-2_amd64.deb ...[0m
[0;33m    Unpacking libdeflate0:amd64 (1.10-2) ...[0m
[0;33m    Selecting previously unselected package libjbig0:amd64.[0m
[0;33m    Preparing to unpack .../45-libjbig0_2.1-3.1ubuntu0.22.04.1_amd64.deb ...[0m
[0;33m    Unpacking libjbig0:amd64 (2.1-3.1ubuntu0.22.04.1) ...[0m
[0;33m    Selecting previously unselected package libwebp7:amd64.[0m
[0;33m    Preparing to unpack .../46-libwebp7_1.2.2-2ubuntu0.22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libwebp7:amd64 (1.2.2-2ubuntu0.22.04.2) ...[0m
[0;33m    Selecting previously unselected package libtiff5:amd64.[0m
[0;33m    Preparing to unpack .../47-libtiff5_4.3.0-6ubuntu0.11_amd64.deb ...[0m
[0;33m    Unpacking libtiff5:amd64 (4.3.0-6ubuntu0.11) ...[0m
[0;33m    Selecting previously unselected package libxpm4:amd64.[0m
[0;33m    Preparing to unpack .../48-libxpm4_1%3a3.5.12-1ubuntu0.22.04.2_amd64.deb ...[0m
[0;33m    Unpacking libxpm4:amd64 (1:3.5.12-1ubuntu0.22.04.2) ...[0m
[0;33m    Selecting previously unselected package libgd3:amd64.[0m
[0;33m    Preparing to unpack .../49-libgd3_2.3.0-2ubuntu2.3_amd64.deb ...[0m
[0;33m    Unpacking libgd3:amd64 (2.3.0-2ubuntu2.3) ...[0m
[0;33m    Selecting previously unselected package libc-devtools.[0m
[0;33m    Preparing to unpack .../50-libc-devtools_2.35-0ubuntu3.10_amd64.deb ...[0m
[0;33m    Unpacking libc-devtools (2.35-0ubuntu3.10) ...[0m
[0;33m    Selecting previously unselected package libfile-fcntllock-perl.[0m
[0;33m    Preparing to unpack .../51-libfile-fcntllock-perl_0.22-3build7_amd64.deb ...[0m
[0;33m    Unpacking libfile-fcntllock-perl (0.22-3build7) ...[0m
[0;33m    Selecting previously unselected package manpages-dev.[0m
[0;33m    Preparing to unpack .../52-manpages-dev_5.10-1ubuntu1_all.deb ...[0m
[0;33m    Unpacking manpages-dev (5.10-1ubuntu1) ...[0m
[0;33m    Selecting previously unselected package tree.[0m
[0;33m    Preparing to unpack .../53-tree_2.0.2-1_amd64.deb ...[0m
[0;33m    Unpacking tree (2.0.2-1) ...[0m
[0;33m    Selecting previously unselected package unzip.[0m
[0;33m    Preparing to unpack .../54-unzip_6.0-26ubuntu3.2_amd64.deb ...[0m
[0;33m    Unpacking unzip (6.0-26ubuntu3.2) ...[0m
[0;33m    Setting up gcc-11-base:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up manpages-dev (5.10-1ubuntu1) ...[0m
[0;33m    Setting up lto-disabled-list (24) ...[0m
[0;33m    Setting up libxpm4:amd64 (1:3.5.12-1ubuntu0.22.04.2) ...[0m
[0;33m    Setting up apt-transport-https (2.4.14) ...[0m
[0;33m    Setting up libfile-fcntllock-perl (0.22-3build7) ...[0m
[0;33m    Setting up libalgorithm-diff-perl (1.201-1) ...[0m
[0;33m    Setting up unzip (6.0-26ubuntu3.2) ...[0m
[0;33m    Setting up libdeflate0:amd64 (1.10-2) ...[0m
[0;33m    Setting up linux-libc-dev:amd64 (5.15.0-152.162) ...[0m
[0;33m    Setting up libgomp1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up bzip2 (1.0.8-5build1) ...[0m
[0;33m    Setting up libjbig0:amd64 (2.1-3.1ubuntu0.22.04.1) ...[0m
[0;33m    Setting up libfakeroot:amd64 (1.28-1ubuntu1) ...[0m
[0;33m    Setting up libasan6:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up fakeroot (1.28-1ubuntu1) ...[0m
[0;33m    update-alternatives: using /usr/bin/fakeroot-sysv to provide /usr/bin/fakeroot (fakeroot) in auto mode[0m
[0;33m    Setting up libtirpc-dev:amd64 (1.3.2-2ubuntu0.1) ...[0m
[0;33m    Setting up rpcsvc-proto (1.4.2-0ubuntu6) ...[0m
[0;33m    Setting up make (4.3-4.1build1) ...[0m
[0;33m    Setting up libquadmath0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up tree (2.0.2-1) ...[0m
[0;33m    Setting up libmpc3:amd64 (1.2.1-2build1) ...[0m
[0;33m    Setting up libatomic1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up fonts-dejavu-core (2.37-2build1) ...[0m
[0;33m    Setting up libjpeg-turbo8:amd64 (2.1.2-0ubuntu1) ...[0m
[0;33m    Setting up libdpkg-perl (1.21.1ubuntu2.3) ...[0m
[0;33m    Setting up libwebp7:amd64 (1.2.2-2ubuntu0.22.04.2) ...[0m
[0;33m    Setting up libubsan1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up libnsl-dev:amd64 (1.3.0-2build2) ...[0m
[0;33m    Setting up libcrypt-dev:amd64 (1:4.4.27-1) ...[0m
[0;33m    Setting up libisl23:amd64 (0.24-2build1) ...[0m
[0;33m    Setting up libc-dev-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    Setting up libalgorithm-diff-xs-perl (0.04-6build3) ...[0m
[0;33m    Setting up libcc1-0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up liblsan0:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up libitm1:amd64 (12.3.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up libalgorithm-merge-perl (0.08-3) ...[0m
[0;33m    Setting up libtsan0:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up libjpeg8:amd64 (8c-2ubuntu10) ...[0m
[0;33m    Setting up cpp-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up fontconfig-config (2.13.1-4.2ubuntu5) ...[0m
[0;33m    Setting up dpkg-dev (1.21.1ubuntu2.3) ...[0m
[0;33m    Setting up libgcc-11-dev:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up gcc-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up cpp (4:11.2.0-1ubuntu1) ...[0m
[0;33m    Setting up libc6-dev:amd64 (2.35-0ubuntu3.10) ...[0m
[0;33m    Setting up libtiff5:amd64 (4.3.0-6ubuntu0.11) ...[0m
[0;33m    Setting up libfontconfig1:amd64 (2.13.1-4.2ubuntu5) ...[0m
[0;33m    Setting up gcc (4:11.2.0-1ubuntu1) ...[0m
[0;33m    Setting up libgd3:amd64 (2.3.0-2ubuntu2.3) ...[0m
[0;33m    Setting up libstdc++-11-dev:amd64 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up libc-devtools (2.35-0ubuntu3.10) ...[0m
[0;33m    Setting up g++-11 (11.4.0-1ubuntu1~22.04.2) ...[0m
[0;33m    Setting up g++ (4:11.2.0-1ubuntu1) ...[0m
[0;33m    update-alternatives: using /usr/bin/g++ to provide /usr/bin/c++ (c++) in auto mode[0m
[0;33m    Setting up build-essential (12.9ubuntu3) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    Processing triggers for libc-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Install Python packages] *************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755775719[0m
[0;33m  cache_updated: false[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following additional packages will be installed:[0m
[0;33m      javascript-common libexpat1-dev libjs-jquery libjs-sphinxdoc[0m
[0;33m      libjs-underscore libpython3-dev libpython3.10-dev python3-pip-whl[0m
[0;33m      python3-setuptools-whl python3-wheel python3.10-dev python3.10-venv[0m
[0;33m      zlib1g-dev[0m
[0;33m    Suggested packages:[0m
[0;33m      apache2 | lighttpd | httpd[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      javascript-common libexpat1-dev libjs-jquery libjs-sphinxdoc[0m
[0;33m      libjs-underscore libpython3-dev libpython3.10-dev python3-dev python3-pip[0m
[0;33m      python3-pip-whl python3-setuptools-whl python3-venv python3-wheel[0m
[0;33m      python3.10-dev python3.10-venv zlib1g-dev[0m
[0;33m    0 upgraded, 16 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 10.0 MB of archives.[0m
[0;33m    After this operation, 34.6 MB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 javascript-common all 11+nmu1 [5936 B][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libexpat1-dev amd64 2.4.7-1ubuntu0.6 [148 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libjs-jquery all 3.6.0+dfsg+~3.5.13-1 [321 kB][0m
[0;33m    Get:4 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libjs-underscore all 1.13.2~dfsg-2 [118 kB][0m
[0;33m    Get:5 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libjs-sphinxdoc all 4.3.2-1 [139 kB][0m
[0;33m    Get:6 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 zlib1g-dev amd64 1:1.2.11.dfsg-2ubuntu9.2 [164 kB][0m
[0;33m    Get:7 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libpython3.10-dev amd64 3.10.12-1~22.04.10 [4763 kB][0m
[0;33m    Get:8 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libpython3-dev amd64 3.10.6-1~22.04.1 [7064 B][0m
[0;33m    Get:9 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3.10-dev amd64 3.10.12-1~22.04.10 [508 kB][0m
[0;33m    Get:10 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 python3-dev amd64 3.10.6-1~22.04.1 [26.0 kB][0m
[0;33m    Get:11 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3-wheel all 0.37.1-2ubuntu0.22.04.1 [32.0 kB][0m
[0;33m    Get:12 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3-pip all 22.0.2+dfsg-1ubuntu0.6 [1306 kB][0m
[0;33m    Get:13 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3-pip-whl all 22.0.2+dfsg-1ubuntu0.6 [1680 kB][0m
[0;33m    Get:14 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3-setuptools-whl all 59.6.0-1.2ubuntu0.22.04.3 [789 kB][0m
[0;33m    Get:15 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3.10-venv amd64 3.10.12-1~22.04.10 [5722 B][0m
[0;33m    Get:16 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/universe amd64 python3-venv amd64 3.10.6-1~22.04.1 [1042 B][0m
[0;33m    Fetched 10.0 MB in 0s (54.9 MB/s)[0m
[0;33m    Selecting previously unselected package javascript-common.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 102348 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../00-javascript-common_11+nmu1_all.deb ...[0m
[0;33m    Unpacking javascript-common (11+nmu1) ...[0m
[0;33m    Selecting previously unselected package libexpat1-dev:amd64.[0m
[0;33m    Preparing to unpack .../01-libexpat1-dev_2.4.7-1ubuntu0.6_amd64.deb ...[0m
[0;33m    Unpacking libexpat1-dev:amd64 (2.4.7-1ubuntu0.6) ...[0m
[0;33m    Selecting previously unselected package libjs-jquery.[0m
[0;33m    Preparing to unpack .../02-libjs-jquery_3.6.0+dfsg+~3.5.13-1_all.deb ...[0m
[0;33m    Unpacking libjs-jquery (3.6.0+dfsg+~3.5.13-1) ...[0m
[0;33m    Selecting previously unselected package libjs-underscore.[0m
[0;33m    Preparing to unpack .../03-libjs-underscore_1.13.2~dfsg-2_all.deb ...[0m
[0;33m    Unpacking libjs-underscore (1.13.2~dfsg-2) ...[0m
[0;33m    Selecting previously unselected package libjs-sphinxdoc.[0m
[0;33m    Preparing to unpack .../04-libjs-sphinxdoc_4.3.2-1_all.deb ...[0m
[0;33m    Unpacking libjs-sphinxdoc (4.3.2-1) ...[0m
[0;33m    Selecting previously unselected package zlib1g-dev:amd64.[0m
[0;33m    Preparing to unpack .../05-zlib1g-dev_1%3a1.2.11.dfsg-2ubuntu9.2_amd64.deb ...[0m
[0;33m    Unpacking zlib1g-dev:amd64 (1:1.2.11.dfsg-2ubuntu9.2) ...[0m
[0;33m    Selecting previously unselected package libpython3.10-dev:amd64.[0m
[0;33m    Preparing to unpack .../06-libpython3.10-dev_3.10.12-1~22.04.10_amd64.deb ...[0m
[0;33m    Unpacking libpython3.10-dev:amd64 (3.10.12-1~22.04.10) ...[0m
[0;33m    Selecting previously unselected package libpython3-dev:amd64.[0m
[0;33m    Preparing to unpack .../07-libpython3-dev_3.10.6-1~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking libpython3-dev:amd64 (3.10.6-1~22.04.1) ...[0m
[0;33m    Selecting previously unselected package python3.10-dev.[0m
[0;33m    Preparing to unpack .../08-python3.10-dev_3.10.12-1~22.04.10_amd64.deb ...[0m
[0;33m    Unpacking python3.10-dev (3.10.12-1~22.04.10) ...[0m
[0;33m    Selecting previously unselected package python3-dev.[0m
[0;33m    Preparing to unpack .../09-python3-dev_3.10.6-1~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking python3-dev (3.10.6-1~22.04.1) ...[0m
[0;33m    Selecting previously unselected package python3-wheel.[0m
[0;33m    Preparing to unpack .../10-python3-wheel_0.37.1-2ubuntu0.22.04.1_all.deb ...[0m
[0;33m    Unpacking python3-wheel (0.37.1-2ubuntu0.22.04.1) ...[0m
[0;33m    Selecting previously unselected package python3-pip.[0m
[0;33m    Preparing to unpack .../11-python3-pip_22.0.2+dfsg-1ubuntu0.6_all.deb ...[0m
[0;33m    Unpacking python3-pip (22.0.2+dfsg-1ubuntu0.6) ...[0m
[0;33m    Selecting previously unselected package python3-pip-whl.[0m
[0;33m    Preparing to unpack .../12-python3-pip-whl_22.0.2+dfsg-1ubuntu0.6_all.deb ...[0m
[0;33m    Unpacking python3-pip-whl (22.0.2+dfsg-1ubuntu0.6) ...[0m
[0;33m    Selecting previously unselected package python3-setuptools-whl.[0m
[0;33m    Preparing to unpack .../13-python3-setuptools-whl_59.6.0-1.2ubuntu0.22.04.3_all.deb ...[0m
[0;33m    Unpacking python3-setuptools-whl (59.6.0-1.2ubuntu0.22.04.3) ...[0m
[0;33m    Selecting previously unselected package python3.10-venv.[0m
[0;33m    Preparing to unpack .../14-python3.10-venv_3.10.12-1~22.04.10_amd64.deb ...[0m
[0;33m    Unpacking python3.10-venv (3.10.12-1~22.04.10) ...[0m
[0;33m    Selecting previously unselected package python3-venv.[0m
[0;33m    Preparing to unpack .../15-python3-venv_3.10.6-1~22.04.1_amd64.deb ...[0m
[0;33m    Unpacking python3-venv (3.10.6-1~22.04.1) ...[0m
[0;33m    Setting up javascript-common (11+nmu1) ...[0m
[0;33m    Setting up python3-setuptools-whl (59.6.0-1.2ubuntu0.22.04.3) ...[0m
[0;33m    Setting up python3-pip-whl (22.0.2+dfsg-1ubuntu0.6) ...[0m
[0;33m    Setting up python3-wheel (0.37.1-2ubuntu0.22.04.1) ...[0m
[0;33m    Setting up libexpat1-dev:amd64 (2.4.7-1ubuntu0.6) ...[0m
[0;33m    Setting up python3-pip (22.0.2+dfsg-1ubuntu0.6) ...[0m
[0;33m    Setting up zlib1g-dev:amd64 (1:1.2.11.dfsg-2ubuntu9.2) ...[0m
[0;33m    Setting up libjs-jquery (3.6.0+dfsg+~3.5.13-1) ...[0m
[0;33m    Setting up libjs-underscore (1.13.2~dfsg-2) ...[0m
[0;33m    Setting up python3.10-venv (3.10.12-1~22.04.10) ...[0m
[0;33m    Setting up python3-venv (3.10.6-1~22.04.1) ...[0m
[0;33m    Setting up libpython3.10-dev:amd64 (3.10.12-1~22.04.10) ...[0m
[0;33m    Setting up libjs-sphinxdoc (4.3.2-1) ...[0m
[0;33m    Setting up python3.10-dev (3.10.12-1~22.04.10) ...[0m
[0;33m    Setting up libpython3-dev:amd64 (3.10.6-1~22.04.1) ...[0m
[0;33m    Setting up python3-dev (3.10.6-1~22.04.1) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Create deploy user] ******************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  comment: ''[0m
[0;33m  create_home: true[0m
[0;33m  group: 1001[0m
[0;33m  groups: sudo[0m
[0;33m  home: /home/<USER>
[0;33m  name: deploy[0m
[0;33m  shell: /bin/bash[0m
[0;33m  state: present[0m
[0;33m  system: false[0m
[0;33m  uid: 1001[0m

TASK [Set up sudo access for deploy user] **************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  backup: ''[0m
[0;33m  msg: line added and ownership, perms or SE linux context changed[0m

TASK [Create .ssh directory for deploy user] ***********************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  mode: '0700'[0m
[0;33m  owner: deploy[0m
[0;33m  path: /home/<USER>/.ssh[0m
[0;33m  size: 4096[0m
[0;33m  state: directory[0m
[0;33m  uid: 1001[0m

TASK [Copy authorized_keys to deploy user] *************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: 53249e97a495e62e80241dae23b996a8c4e592d0[0m
[0;33m  dest: /home/<USER>/.ssh/authorized_keys[0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  md5sum: a661a4116303e65c1e390da634875fd1[0m
[0;33m  mode: '0600'[0m
[0;33m  owner: deploy[0m
[0;33m  size: 742[0m
[0;33m  src: /home/<USER>/.ssh/authorized_keys[0m
[0;33m  state: file[0m
[0;33m  uid: 1001[0m

TASK [Set timezone to UTC] *****************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  msg: executed `/usr/bin/timedatectl set-timezone UTC`[0m

TASK [Configure automatic security updates] ************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cache_update_time: 1755775719[0m
[0;32m  cache_updated: false[0m

TASK [Configure unattended-upgrades] *******************************************
[0;36mskipping: [web_server] => changed=false [0m
[0;36m  false_condition: false[0m
[0;36m  skip_reason: Conditional result was False[0m

TASK [Enable automatic security updates] ***************************************
[0;32mok: [web_server] => (item=APT::Periodic::Update-Package-Lists "1";) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  backup: ''[0m
[0;32m  item: APT::Periodic::Update-Package-Lists "1";[0m
[0;32m  msg: ''[0m
[0;32mok: [web_server] => (item=APT::Periodic::Unattended-Upgrade "1";) => changed=false [0m
[0;32m  ansible_loop_var: item[0m
[0;32m  backup: ''[0m
[0;32m  item: APT::Periodic::Unattended-Upgrade "1";[0m
[0;32m  msg: ''[0m

TASK [Install and configure fail2ban] ******************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755775719[0m
[0;33m  cache_updated: false[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following additional packages will be installed:[0m
[0;33m      python3-pyinotify whois[0m
[0;33m    Suggested packages:[0m
[0;33m      mailx monit sqlite3 python-pyinotify-doc[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      fail2ban python3-pyinotify whois[0m
[0;33m    0 upgraded, 3 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 473 kB of archives.[0m
[0;33m    After this operation, 2486 kB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/universe amd64 fail2ban all 0.11.2-6 [394 kB][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 python3-pyinotify all 0.9.6-1.3 [24.8 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 whois amd64 5.5.13 [53.4 kB][0m
[0;33m    Fetched 473 kB in 0s (6693 kB/s)[0m
[0;33m    Selecting previously unselected package fail2ban.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 103417 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../fail2ban_0.11.2-6_all.deb ...[0m
[0;33m    Unpacking fail2ban (0.11.2-6) ...[0m
[0;33m    Selecting previously unselected package python3-pyinotify.[0m
[0;33m    Preparing to unpack .../python3-pyinotify_0.9.6-1.3_all.deb ...[0m
[0;33m    Unpacking python3-pyinotify (0.9.6-1.3) ...[0m
[0;33m    Selecting previously unselected package whois.[0m
[0;33m    Preparing to unpack .../whois_5.5.13_amd64.deb ...[0m
[0;33m    Unpacking whois (5.5.13) ...[0m
[0;33m    Setting up whois (5.5.13) ...[0m
[0;33m    Setting up fail2ban (0.11.2-6) ...[0m
[0;33m    Setting up python3-pyinotify (0.9.6-1.3) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Start and enable fail2ban] ***********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  enabled: true[0m
[0;33m  name: fail2ban[0m
[0;33m  state: started[0m
[0;33m  status:[0m
[0;33m    ActiveEnterTimestamp: n/a[0m
[0;33m    ActiveEnterTimestampMonotonic: '0'[0m
[0;33m    ActiveExitTimestamp: n/a[0m
[0;33m    ActiveExitTimestampMonotonic: '0'[0m
[0;33m    ActiveState: inactive[0m
[0;33m    After: ipset.service system.slice ip6tables.service systemd-journald.socket sysinit.target iptables.service firewalld.service -.mount network.target nftables.service basic.target[0m
[0;33m    AllowIsolate: 'no'[0m
[0;33m    AssertResult: 'no'[0m
[0;33m    AssertTimestamp: n/a[0m
[0;33m    AssertTimestampMonotonic: '0'[0m
[0;33m    Before: shutdown.target[0m
[0;33m    BlockIOAccounting: 'no'[0m
[0;33m    BlockIOWeight: '[not set]'[0m
[0;33m    CPUAccounting: 'yes'[0m
[0;33m    CPUAffinityFromNUMA: 'no'[0m
[0;33m    CPUQuotaPerSecUSec: infinity[0m
[0;33m    CPUQuotaPeriodUSec: infinity[0m
[0;33m    CPUSchedulingPolicy: '0'[0m
[0;33m    CPUSchedulingPriority: '0'[0m
[0;33m    CPUSchedulingResetOnFork: 'no'[0m
[0;33m    CPUShares: '[not set]'[0m
[0;33m    CPUUsageNSec: '[not set]'[0m
[0;33m    CPUWeight: '[not set]'[0m
[0;33m    CacheDirectoryMode: '0755'[0m
[0;33m    CanClean: runtime[0m
[0;33m    CanFreeze: 'yes'[0m
[0;33m    CanIsolate: 'no'[0m
[0;33m    CanReload: 'yes'[0m
[0;33m    CanStart: 'yes'[0m
[0;33m    CanStop: 'yes'[0m
[0;33m    CapabilityBoundingSet: cap_chown cap_dac_override cap_dac_read_search cap_fowner cap_fsetid cap_kill cap_setgid cap_setuid cap_setpcap cap_linux_immutable cap_net_bind_service cap_net_broadcast cap_net_admin cap_net_raw cap_ipc_lock cap_ipc_owner cap_sys_module cap_sys_rawio cap_sys_chroot cap_sys_ptrace cap_sys_pacct cap_sys_admin cap_sys_boot cap_sys_nice cap_sys_resource cap_sys_time cap_sys_tty_config cap_mknod cap_lease cap_audit_write cap_audit_control cap_setfcap cap_mac_override cap_mac_admin cap_syslog cap_wake_alarm cap_block_suspend cap_audit_read cap_perfmon cap_bpf cap_checkpoint_restore[0m
[0;33m    CleanResult: success[0m
[0;33m    CollectMode: inactive[0m
[0;33m    ConditionResult: 'no'[0m
[0;33m    ConditionTimestamp: n/a[0m
[0;33m    ConditionTimestampMonotonic: '0'[0m
[0;33m    ConfigurationDirectoryMode: '0755'[0m
[0;33m    Conflicts: shutdown.target[0m
[0;33m    ControlPID: '0'[0m
[0;33m    CoredumpFilter: '0x33'[0m
[0;33m    DefaultDependencies: 'yes'[0m
[0;33m    DefaultMemoryLow: '0'[0m
[0;33m    DefaultMemoryMin: '0'[0m
[0;33m    Delegate: 'no'[0m
[0;33m    Description: Fail2Ban Service[0m
[0;33m    DevicePolicy: auto[0m
[0;33m    Documentation: '"man:fail2ban(1)"'[0m
[0;33m    DynamicUser: 'no'[0m
[0;33m    Environment: PYTHONNOUSERSITE=yes[0m
[0;33m    ExecMainCode: '0'[0m
[0;33m    ExecMainExitTimestamp: n/a[0m
[0;33m    ExecMainExitTimestampMonotonic: '0'[0m
[0;33m    ExecMainPID: '0'[0m
[0;33m    ExecMainStartTimestamp: n/a[0m
[0;33m    ExecMainStartTimestampMonotonic: '0'[0m
[0;33m    ExecMainStatus: '0'[0m
[0;33m    ExecReload: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client reload ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecReloadEx: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client reload ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStart: '{ path=/usr/bin/fail2ban-server ; argv[]=/usr/bin/fail2ban-server -xf start ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStartEx: '{ path=/usr/bin/fail2ban-server ; argv[]=/usr/bin/fail2ban-server -xf start ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStop: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client stop ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    ExecStopEx: '{ path=/usr/bin/fail2ban-client ; argv[]=/usr/bin/fail2ban-client stop ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;33m    FailureAction: none[0m
[0;33m    FileDescriptorStoreMax: '0'[0m
[0;33m    FinalKillSignal: '9'[0m
[0;33m    FragmentPath: /lib/systemd/system/fail2ban.service[0m
[0;33m    FreezerState: running[0m
[0;33m    GID: '[not set]'[0m
[0;33m    GuessMainPID: 'yes'[0m
[0;33m    IOAccounting: 'no'[0m
[0;33m    IOReadBytes: '18446744073709551615'[0m
[0;33m    IOReadOperations: '18446744073709551615'[0m
[0;33m    IOSchedulingClass: '2'[0m
[0;33m    IOSchedulingPriority: '4'[0m
[0;33m    IOWeight: '[not set]'[0m
[0;33m    IOWriteBytes: '18446744073709551615'[0m
[0;33m    IOWriteOperations: '18446744073709551615'[0m
[0;33m    IPAccounting: 'no'[0m
[0;33m    IPEgressBytes: '[no data]'[0m
[0;33m    IPEgressPackets: '[no data]'[0m
[0;33m    IPIngressBytes: '[no data]'[0m
[0;33m    IPIngressPackets: '[no data]'[0m
[0;33m    Id: fail2ban.service[0m
[0;33m    IgnoreOnIsolate: 'no'[0m
[0;33m    IgnoreSIGPIPE: 'yes'[0m
[0;33m    InactiveEnterTimestamp: n/a[0m
[0;33m    InactiveEnterTimestampMonotonic: '0'[0m
[0;33m    InactiveExitTimestamp: n/a[0m
[0;33m    InactiveExitTimestampMonotonic: '0'[0m
[0;33m    JobRunningTimeoutUSec: infinity[0m
[0;33m    JobTimeoutAction: none[0m
[0;33m    JobTimeoutUSec: infinity[0m
[0;33m    KeyringMode: private[0m
[0;33m    KillMode: control-group[0m
[0;33m    KillSignal: '15'[0m
[0;33m    LimitAS: infinity[0m
[0;33m    LimitASSoft: infinity[0m
[0;33m    LimitCORE: infinity[0m
[0;33m    LimitCORESoft: '0'[0m
[0;33m    LimitCPU: infinity[0m
[0;33m    LimitCPUSoft: infinity[0m
[0;33m    LimitDATA: infinity[0m
[0;33m    LimitDATASoft: infinity[0m
[0;33m    LimitFSIZE: infinity[0m
[0;33m    LimitFSIZESoft: infinity[0m
[0;33m    LimitLOCKS: infinity[0m
[0;33m    LimitLOCKSSoft: infinity[0m
[0;33m    LimitMEMLOCK: '8388608'[0m
[0;33m    LimitMEMLOCKSoft: '8388608'[0m
[0;33m    LimitMSGQUEUE: '819200'[0m
[0;33m    LimitMSGQUEUESoft: '819200'[0m
[0;33m    LimitNICE: '0'[0m
[0;33m    LimitNICESoft: '0'[0m
[0;33m    LimitNOFILE: '524288'[0m
[0;33m    LimitNOFILESoft: '1024'[0m
[0;33m    LimitNPROC: '3577'[0m
[0;33m    LimitNPROCSoft: '3577'[0m
[0;33m    LimitRSS: infinity[0m
[0;33m    LimitRSSSoft: infinity[0m
[0;33m    LimitRTPRIO: '0'[0m
[0;33m    LimitRTPRIOSoft: '0'[0m
[0;33m    LimitRTTIME: infinity[0m
[0;33m    LimitRTTIMESoft: infinity[0m
[0;33m    LimitSIGPENDING: '3577'[0m
[0;33m    LimitSIGPENDINGSoft: '3577'[0m
[0;33m    LimitSTACK: infinity[0m
[0;33m    LimitSTACKSoft: '8388608'[0m
[0;33m    LoadState: loaded[0m
[0;33m    LockPersonality: 'no'[0m
[0;33m    LogLevelMax: '-1'[0m
[0;33m    LogRateLimitBurst: '0'[0m
[0;33m    LogRateLimitIntervalUSec: '0'[0m
[0;33m    LogsDirectoryMode: '0755'[0m
[0;33m    MainPID: '0'[0m
[0;33m    ManagedOOMMemoryPressure: auto[0m
[0;33m    ManagedOOMMemoryPressureLimit: '0'[0m
[0;33m    ManagedOOMPreference: none[0m
[0;33m    ManagedOOMSwap: auto[0m
[0;33m    MemoryAccounting: 'yes'[0m
[0;33m    MemoryAvailable: infinity[0m
[0;33m    MemoryCurrent: '[not set]'[0m
[0;33m    MemoryDenyWriteExecute: 'no'[0m
[0;33m    MemoryHigh: infinity[0m
[0;33m    MemoryLimit: infinity[0m
[0;33m    MemoryLow: '0'[0m
[0;33m    MemoryMax: infinity[0m
[0;33m    MemoryMin: '0'[0m
[0;33m    MemorySwapMax: infinity[0m
[0;33m    MountAPIVFS: 'no'[0m
[0;33m    NFileDescriptorStore: '0'[0m
[0;33m    NRestarts: '0'[0m
[0;33m    NUMAPolicy: n/a[0m
[0;33m    Names: fail2ban.service[0m
[0;33m    NeedDaemonReload: 'no'[0m
[0;33m    Nice: '0'[0m
[0;33m    NoNewPrivileges: 'no'[0m
[0;33m    NonBlocking: 'no'[0m
[0;33m    NotifyAccess: none[0m
[0;33m    OOMPolicy: stop[0m
[0;33m    OOMScoreAdjust: '0'[0m
[0;33m    OnFailureJobMode: replace[0m
[0;33m    OnSuccessJobMode: fail[0m
[0;33m    PIDFile: /run/fail2ban/fail2ban.pid[0m
[0;33m    PartOf: firewalld.service[0m
[0;33m    Perpetual: 'no'[0m
[0;33m    PrivateDevices: 'no'[0m
[0;33m    PrivateIPC: 'no'[0m
[0;33m    PrivateMounts: 'no'[0m
[0;33m    PrivateNetwork: 'no'[0m
[0;33m    PrivateTmp: 'no'[0m
[0;33m    PrivateUsers: 'no'[0m
[0;33m    ProcSubset: all[0m
[0;33m    ProtectClock: 'no'[0m
[0;33m    ProtectControlGroups: 'no'[0m
[0;33m    ProtectHome: 'no'[0m
[0;33m    ProtectHostname: 'no'[0m
[0;33m    ProtectKernelLogs: 'no'[0m
[0;33m    ProtectKernelModules: 'no'[0m
[0;33m    ProtectKernelTunables: 'no'[0m
[0;33m    ProtectProc: default[0m
[0;33m    ProtectSystem: 'no'[0m
[0;33m    RefuseManualStart: 'no'[0m
[0;33m    RefuseManualStop: 'no'[0m
[0;33m    ReloadResult: success[0m
[0;33m    RemainAfterExit: 'no'[0m
[0;33m    RemoveIPC: 'no'[0m
[0;33m    Requires: sysinit.target system.slice -.mount[0m
[0;33m    RequiresMountsFor: /run/fail2ban[0m
[0;33m    Restart: on-failure[0m
[0;33m    RestartKillSignal: '15'[0m
[0;33m    RestartPreventExitStatus: 0 255[0m
[0;33m    RestartUSec: 100ms[0m
[0;33m    RestrictNamespaces: 'no'[0m
[0;33m    RestrictRealtime: 'no'[0m
[0;33m    RestrictSUIDSGID: 'no'[0m
[0;33m    Result: success[0m
[0;33m    RootDirectoryStartOnly: 'no'[0m
[0;33m    RuntimeDirectory: fail2ban[0m
[0;33m    RuntimeDirectoryMode: '0755'[0m
[0;33m    RuntimeDirectoryPreserve: 'no'[0m
[0;33m    RuntimeMaxUSec: infinity[0m
[0;33m    SameProcessGroup: 'no'[0m
[0;33m    SecureBits: '0'[0m
[0;33m    SendSIGHUP: 'no'[0m
[0;33m    SendSIGKILL: 'yes'[0m
[0;33m    Slice: system.slice[0m
[0;33m    StandardError: inherit[0m
[0;33m    StandardInput: 'null'[0m
[0;33m    StandardOutput: journal[0m
[0;33m    StartLimitAction: none[0m
[0;33m    StartLimitBurst: '5'[0m
[0;33m    StartLimitIntervalUSec: 10s[0m
[0;33m    StartupBlockIOWeight: '[not set]'[0m
[0;33m    StartupCPUShares: '[not set]'[0m
[0;33m    StartupCPUWeight: '[not set]'[0m
[0;33m    StartupIOWeight: '[not set]'[0m
[0;33m    StateChangeTimestamp: n/a[0m
[0;33m    StateChangeTimestampMonotonic: '0'[0m
[0;33m    StateDirectoryMode: '0755'[0m
[0;33m    StatusErrno: '0'[0m
[0;33m    StopWhenUnneeded: 'no'[0m
[0;33m    SubState: dead[0m
[0;33m    SuccessAction: none[0m
[0;33m    SyslogFacility: '3'[0m
[0;33m    SyslogLevel: '6'[0m
[0;33m    SyslogLevelPrefix: 'yes'[0m
[0;33m    SyslogPriority: '30'[0m
[0;33m    SystemCallErrorNumber: '**********'[0m
[0;33m    TTYReset: 'no'[0m
[0;33m    TTYVHangup: 'no'[0m
[0;33m    TTYVTDisallocate: 'no'[0m
[0;33m    TasksAccounting: 'yes'[0m
[0;33m    TasksCurrent: '[not set]'[0m
[0;33m    TasksMax: '1073'[0m
[0;33m    TimeoutAbortUSec: 1min 30s[0m
[0;33m    TimeoutCleanUSec: infinity[0m
[0;33m    TimeoutStartFailureMode: terminate[0m
[0;33m    TimeoutStartUSec: 1min 30s[0m
[0;33m    TimeoutStopFailureMode: terminate[0m
[0;33m    TimeoutStopUSec: 1min 30s[0m
[0;33m    TimerSlackNSec: '50000'[0m
[0;33m    Transient: 'no'[0m
[0;33m    Type: simple[0m
[0;33m    UID: '[not set]'[0m
[0;33m    UMask: '0022'[0m
[0;33m    UnitFilePreset: enabled[0m
[0;33m    UnitFileState: disabled[0m
[0;33m    UtmpMode: init[0m
[0;33m    WatchdogSignal: '6'[0m
[0;33m    WatchdogTimestamp: n/a[0m
[0;33m    WatchdogTimestampMonotonic: '0'[0m
[0;33m    WatchdogUSec: infinity[0m

TASK [Create application directories] ******************************************
[0;33mchanged: [web_server] => (item=/opt/apps) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  item: /opt/apps[0m
[0;33m  mode: '0755'[0m
[0;33m  owner: deploy[0m
[0;33m  path: /opt/apps[0m
[0;33m  size: 4096[0m
[0;33m  state: directory[0m
[0;33m  uid: 1001[0m
[0;33mchanged: [web_server] => (item=/var/log/apps) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  item: /var/log/apps[0m
[0;33m  mode: '0755'[0m
[0;33m  owner: deploy[0m
[0;33m  path: /var/log/apps[0m
[0;33m  size: 4096[0m
[0;33m  state: directory[0m
[0;33m  uid: 1001[0m
[0;33mchanged: [web_server] => (item=/home/<USER>/scripts) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  item: /home/<USER>/scripts[0m
[0;33m  mode: '0755'[0m
[0;33m  owner: deploy[0m
[0;33m  path: /home/<USER>/scripts[0m
[0;33m  size: 4096[0m
[0;33m  state: directory[0m
[0;33m  uid: 1001[0m

TASK [Set up basic bash aliases for deploy user] *******************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  msg: Block inserted[0m

TASK [Display bootstrap completion message] ************************************
[0;32mok: [web_server] => [0m
[0;32m  msg: |-[0m
[0;32m    Bootstrap completed successfully![0m
[0;32m    - Deploy user 'deploy' created with sudo access[0m
[0;32m    - Essential packages installed[0m
[0;32m    - Security updates configured[0m
[0;32m    - Fail2ban installed and configured[0m
[0;32m    - Application directories created[0m
[0;32m  [0m
[0;32m    Next steps:[0m
[0;32m    1. Run the hardening playbook[0m
[0;32m    2. Run the application playbook[0m

PLAY RECAP *********************************************************************
[0;33mweb_server[0m                 : [0;32mok=17  [0m [0;33mchanged=13  [0m unreachable=0    failed=0    [0;36mskipped=1   [0m rescued=0    ignored=0   

