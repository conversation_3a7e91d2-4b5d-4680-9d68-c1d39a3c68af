Playbook: app.yml
Return code: 2
================================================================================
STDOUT:
[0;34mNo config file found; using defaults[0m

PLAY [Install Applications (Docker + NGINX)] ***********************************

TASK [Gathering Facts] *********************************************************
[0;32mok: [web_server][0m

TASK [Update apt cache] ********************************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cache_update_time: 1755775719[0m
[0;32m  cache_updated: false[0m

TASK [Install Docker dependencies] *********************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cache_update_time: 1755775719[0m
[0;32m  cache_updated: false[0m

TASK [Add Docker GPG key] ******************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  after:[0m
[0;33m  - 8D81803C0EBFCD88[0m
[0;33m  - 7EA0A9C3F273FCD8[0m
[0;33m  - D94AA3F0EFE21092[0m
[0;33m  - 871920D1991BC93C[0m
[0;33m  before:[0m
[0;33m  - D94AA3F0EFE21092[0m
[0;33m  - 871920D1991BC93C[0m
[0;33m  fp: 8D81803C0EBFCD88[0m
[0;33m  id: 8D81803C0EBFCD88[0m
[0;33m  key_id: 8D81803C0EBFCD88[0m
[0;33m  short_id: 0EBFCD88[0m

TASK [Add Docker repository] ***************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  repo: deb [arch=amd64] https://download.docker.com/linux/ubuntu jammy stable[0m
[0;33m  sources_added:[0m
[0;33m  - /etc/apt/sources.list.d/download_docker_com_linux_ubuntu.list[0m
[0;33m  sources_removed: [][0m
[0;33m  state: present[0m

TASK [Install Docker] **********************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755776343[0m
[0;33m  cache_updated: false[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following additional packages will be installed:[0m
[0;33m      docker-ce-rootless-extras docker-compose-plugin libslirp0 pigz slirp4netns[0m
[0;33m    Suggested packages:[0m
[0;33m      cgroupfs-mount | cgroup-lite docker-model-plugin[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      containerd.io docker-buildx-plugin docker-ce docker-ce-cli[0m
[0;33m      docker-ce-rootless-extras docker-compose-plugin libslirp0 pigz slirp4netns[0m
[0;33m    0 upgraded, 9 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 103 MB of archives.[0m
[0;33m    After this operation, 431 MB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/universe amd64 pigz amd64 2.6-1 [63.6 kB][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/main amd64 libslirp0 amd64 4.6.1-1build1 [61.5 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy/universe amd64 slirp4netns amd64 1.0.1-2 [28.2 kB][0m
[0;33m    Get:4 https://download.docker.com/linux/ubuntu jammy/stable amd64 containerd.io amd64 1.7.27-1 [30.5 MB][0m
[0;33m    Get:5 https://download.docker.com/linux/ubuntu jammy/stable amd64 docker-ce-cli amd64 5:28.3.3-1~ubuntu.22.04~jammy [16.5 MB][0m
[0;33m    Get:6 https://download.docker.com/linux/ubuntu jammy/stable amd64 docker-ce amd64 5:28.3.3-1~ubuntu.22.04~jammy [19.7 MB][0m
[0;33m    Get:7 https://download.docker.com/linux/ubuntu jammy/stable amd64 docker-buildx-plugin amd64 0.26.1-1~ubuntu.22.04~jammy [15.8 MB][0m
[0;33m    Get:8 https://download.docker.com/linux/ubuntu jammy/stable amd64 docker-ce-rootless-extras amd64 5:28.3.3-1~ubuntu.22.04~jammy [6478 kB][0m
[0;33m    Get:9 https://download.docker.com/linux/ubuntu jammy/stable amd64 docker-compose-plugin amd64 2.39.1-1~ubuntu.22.04~jammy [14.3 MB][0m
[0;33m    Fetched 103 MB in 1s (82.7 MB/s)[0m
[0;33m    Selecting previously unselected package containerd.io.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 103877 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../0-containerd.io_1.7.27-1_amd64.deb ...[0m
[0;33m    Unpacking containerd.io (1.7.27-1) ...[0m
[0;33m    Selecting previously unselected package docker-ce-cli.[0m
[0;33m    Preparing to unpack .../1-docker-ce-cli_5%3a28.3.3-1~ubuntu.22.04~jammy_amd64.deb ...[0m
[0;33m    Unpacking docker-ce-cli (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Selecting previously unselected package docker-ce.[0m
[0;33m    Preparing to unpack .../2-docker-ce_5%3a28.3.3-1~ubuntu.22.04~jammy_amd64.deb ...[0m
[0;33m    Unpacking docker-ce (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Selecting previously unselected package pigz.[0m
[0;33m    Preparing to unpack .../3-pigz_2.6-1_amd64.deb ...[0m
[0;33m    Unpacking pigz (2.6-1) ...[0m
[0;33m    Selecting previously unselected package docker-buildx-plugin.[0m
[0;33m    Preparing to unpack .../4-docker-buildx-plugin_0.26.1-1~ubuntu.22.04~jammy_amd64.deb ...[0m
[0;33m    Unpacking docker-buildx-plugin (0.26.1-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Selecting previously unselected package docker-ce-rootless-extras.[0m
[0;33m    Preparing to unpack .../5-docker-ce-rootless-extras_5%3a28.3.3-1~ubuntu.22.04~jammy_amd64.deb ...[0m
[0;33m    Unpacking docker-ce-rootless-extras (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Selecting previously unselected package docker-compose-plugin.[0m
[0;33m    Preparing to unpack .../6-docker-compose-plugin_2.39.1-1~ubuntu.22.04~jammy_amd64.deb ...[0m
[0;33m    Unpacking docker-compose-plugin (2.39.1-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Selecting previously unselected package libslirp0:amd64.[0m
[0;33m    Preparing to unpack .../7-libslirp0_4.6.1-1build1_amd64.deb ...[0m
[0;33m    Unpacking libslirp0:amd64 (4.6.1-1build1) ...[0m
[0;33m    Selecting previously unselected package slirp4netns.[0m
[0;33m    Preparing to unpack .../8-slirp4netns_1.0.1-2_amd64.deb ...[0m
[0;33m    Unpacking slirp4netns (1.0.1-2) ...[0m
[0;33m    Setting up docker-buildx-plugin (0.26.1-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Setting up containerd.io (1.7.27-1) ...[0m
[0;33m    Created symlink /etc/systemd/system/multi-user.target.wants/containerd.service → /lib/systemd/system/containerd.service.[0m
[0;33m    Setting up docker-compose-plugin (2.39.1-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Setting up docker-ce-cli (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Setting up libslirp0:amd64 (4.6.1-1build1) ...[0m
[0;33m    Setting up pigz (2.6-1) ...[0m
[0;33m    Setting up docker-ce-rootless-extras (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Setting up slirp4netns (1.0.1-2) ...[0m
[0;33m    Setting up docker-ce (5:28.3.3-1~ubuntu.22.04~jammy) ...[0m
[0;33m    Created symlink /etc/systemd/system/multi-user.target.wants/docker.service → /lib/systemd/system/docker.service.[0m
[0;33m    Created symlink /etc/systemd/system/sockets.target.wants/docker.socket → /lib/systemd/system/docker.socket.[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    Processing triggers for libc-bin (2.35-0ubuntu3.10) ...[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Start and enable Docker service] *****************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  enabled: true[0m
[0;32m  name: docker[0m
[0;32m  state: started[0m
[0;32m  status:[0m
[0;32m    ActiveEnterTimestamp: Thu 2025-08-21 11:39:32 UTC[0m
[0;32m    ActiveEnterTimestampMonotonic: '**********'[0m
[0;32m    ActiveExitTimestamp: n/a[0m
[0;32m    ActiveExitTimestampMonotonic: '0'[0m
[0;32m    ActiveState: active[0m
[0;32m    After: systemd-journald.socket docker.socket network-online.target system.slice firewalld.service containerd.service sysinit.target basic.target time-set.target nss-lookup.target[0m
[0;32m    AllowIsolate: 'no'[0m
[0;32m    AssertResult: 'yes'[0m
[0;32m    AssertTimestamp: Thu 2025-08-21 11:39:31 UTC[0m
[0;32m    AssertTimestampMonotonic: '**********'[0m
[0;32m    Before: shutdown.target multi-user.target[0m
[0;32m    BlockIOAccounting: 'no'[0m
[0;32m    BlockIOWeight: '[not set]'[0m
[0;32m    CPUAccounting: 'yes'[0m
[0;32m    CPUAffinityFromNUMA: 'no'[0m
[0;32m    CPUQuotaPerSecUSec: infinity[0m
[0;32m    CPUQuotaPeriodUSec: infinity[0m
[0;32m    CPUSchedulingPolicy: '0'[0m
[0;32m    CPUSchedulingPriority: '0'[0m
[0;32m    CPUSchedulingResetOnFork: 'no'[0m
[0;32m    CPUShares: '[not set]'[0m
[0;32m    CPUUsageNSec: '*********'[0m
[0;32m    CPUWeight: '[not set]'[0m
[0;32m    CacheDirectoryMode: '0755'[0m
[0;32m    CanFreeze: 'yes'[0m
[0;32m    CanIsolate: 'no'[0m
[0;32m    CanReload: 'yes'[0m
[0;32m    CanStart: 'yes'[0m
[0;32m    CanStop: 'yes'[0m
[0;32m    CapabilityBoundingSet: cap_chown cap_dac_override cap_dac_read_search cap_fowner cap_fsetid cap_kill cap_setgid cap_setuid cap_setpcap cap_linux_immutable cap_net_bind_service cap_net_broadcast cap_net_admin cap_net_raw cap_ipc_lock cap_ipc_owner cap_sys_module cap_sys_rawio cap_sys_chroot cap_sys_ptrace cap_sys_pacct cap_sys_admin cap_sys_boot cap_sys_nice cap_sys_resource cap_sys_time cap_sys_tty_config cap_mknod cap_lease cap_audit_write cap_audit_control cap_setfcap cap_mac_override cap_mac_admin cap_syslog cap_wake_alarm cap_block_suspend cap_audit_read cap_perfmon cap_bpf cap_checkpoint_restore[0m
[0;32m    CleanResult: success[0m
[0;32m    CollectMode: inactive[0m
[0;32m    ConditionResult: 'yes'[0m
[0;32m    ConditionTimestamp: Thu 2025-08-21 11:39:31 UTC[0m
[0;32m    ConditionTimestampMonotonic: '1034116251'[0m
[0;32m    ConfigurationDirectoryMode: '0755'[0m
[0;32m    Conflicts: shutdown.target[0m
[0;32m    ControlGroup: /system.slice/docker.service[0m
[0;32m    ControlPID: '0'[0m
[0;32m    CoredumpFilter: '0x33'[0m
[0;32m    DefaultDependencies: 'yes'[0m
[0;32m    DefaultMemoryLow: '0'[0m
[0;32m    DefaultMemoryMin: '0'[0m
[0;32m    Delegate: 'yes'[0m
[0;32m    DelegateControllers: cpu cpuacct cpuset io blkio memory devices pids bpf-firewall bpf-devices bpf-foreign bpf-socket-bind[0m
[0;32m    Description: Docker Application Container Engine[0m
[0;32m    DevicePolicy: auto[0m
[0;32m    Documentation: https://docs.docker.com[0m
[0;32m    DynamicUser: 'no'[0m
[0;32m    EffectiveCPUs: 0-1[0m
[0;32m    EffectiveMemoryNodes: '0'[0m
[0;32m    ExecMainCode: '0'[0m
[0;32m    ExecMainExitTimestamp: n/a[0m
[0;32m    ExecMainExitTimestampMonotonic: '0'[0m
[0;32m    ExecMainPID: '12549'[0m
[0;32m    ExecMainStartTimestamp: Thu 2025-08-21 11:39:31 UTC[0m
[0;32m    ExecMainStartTimestampMonotonic: '**********'[0m
[0;32m    ExecMainStatus: '0'[0m
[0;32m    ExecReload: '{ path=/bin/kill ; argv[]=/bin/kill -s HUP $MAINPID ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecReloadEx: '{ path=/bin/kill ; argv[]=/bin/kill -s HUP $MAINPID ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecStart: '{ path=/usr/bin/dockerd ; argv[]=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock ; ignore_errors=no ; start_time=[Thu 2025-08-21 11:39:31 UTC] ; stop_time=[n/a] ; pid=12549 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecStartEx: '{ path=/usr/bin/dockerd ; argv[]=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock ; flags= ; start_time=[Thu 2025-08-21 11:39:31 UTC] ; stop_time=[n/a] ; pid=12549 ; code=(null) ; status=0/0 }'[0m
[0;32m    FailureAction: none[0m
[0;32m    FileDescriptorStoreMax: '0'[0m
[0;32m    FinalKillSignal: '9'[0m
[0;32m    FragmentPath: /lib/systemd/system/docker.service[0m
[0;32m    FreezerState: running[0m
[0;32m    GID: '[not set]'[0m
[0;32m    GuessMainPID: 'yes'[0m
[0;32m    IOAccounting: 'no'[0m
[0;32m    IOReadBytes: '18446744073709551615'[0m
[0;32m    IOReadOperations: '18446744073709551615'[0m
[0;32m    IOSchedulingClass: '2'[0m
[0;32m    IOSchedulingPriority: '4'[0m
[0;32m    IOWeight: '[not set]'[0m
[0;32m    IOWriteBytes: '18446744073709551615'[0m
[0;32m    IOWriteOperations: '18446744073709551615'[0m
[0;32m    IPAccounting: 'no'[0m
[0;32m    IPEgressBytes: '[no data]'[0m
[0;32m    IPEgressPackets: '[no data]'[0m
[0;32m    IPIngressBytes: '[no data]'[0m
[0;32m    IPIngressPackets: '[no data]'[0m
[0;32m    Id: docker.service[0m
[0;32m    IgnoreOnIsolate: 'no'[0m
[0;32m    IgnoreSIGPIPE: 'yes'[0m
[0;32m    InactiveEnterTimestamp: n/a[0m
[0;32m    InactiveEnterTimestampMonotonic: '0'[0m
[0;32m    InactiveExitTimestamp: Thu 2025-08-21 11:39:31 UTC[0m
[0;32m    InactiveExitTimestampMonotonic: '**********'[0m
[0;32m    InvocationID: 7bfeac7736fb47d4add31c905d3e08c1[0m
[0;32m    JobRunningTimeoutUSec: infinity[0m
[0;32m    JobTimeoutAction: none[0m
[0;32m    JobTimeoutUSec: infinity[0m
[0;32m    KeyringMode: private[0m
[0;32m    KillMode: process[0m
[0;32m    KillSignal: '15'[0m
[0;32m    LimitAS: infinity[0m
[0;32m    LimitASSoft: infinity[0m
[0;32m    LimitCORE: infinity[0m
[0;32m    LimitCORESoft: infinity[0m
[0;32m    LimitCPU: infinity[0m
[0;32m    LimitCPUSoft: infinity[0m
[0;32m    LimitDATA: infinity[0m
[0;32m    LimitDATASoft: infinity[0m
[0;32m    LimitFSIZE: infinity[0m
[0;32m    LimitFSIZESoft: infinity[0m
[0;32m    LimitLOCKS: infinity[0m
[0;32m    LimitLOCKSSoft: infinity[0m
[0;32m    LimitMEMLOCK: '8388608'[0m
[0;32m    LimitMEMLOCKSoft: '8388608'[0m
[0;32m    LimitMSGQUEUE: '819200'[0m
[0;32m    LimitMSGQUEUESoft: '819200'[0m
[0;32m    LimitNICE: '0'[0m
[0;32m    LimitNICESoft: '0'[0m
[0;32m    LimitNOFILE: '524288'[0m
[0;32m    LimitNOFILESoft: '1024'[0m
[0;32m    LimitNPROC: infinity[0m
[0;32m    LimitNPROCSoft: infinity[0m
[0;32m    LimitRSS: infinity[0m
[0;32m    LimitRSSSoft: infinity[0m
[0;32m    LimitRTPRIO: '0'[0m
[0;32m    LimitRTPRIOSoft: '0'[0m
[0;32m    LimitRTTIME: infinity[0m
[0;32m    LimitRTTIMESoft: infinity[0m
[0;32m    LimitSIGPENDING: '3577'[0m
[0;32m    LimitSIGPENDINGSoft: '3577'[0m
[0;32m    LimitSTACK: infinity[0m
[0;32m    LimitSTACKSoft: '8388608'[0m
[0;32m    LoadState: loaded[0m
[0;32m    LockPersonality: 'no'[0m
[0;32m    LogLevelMax: '-1'[0m
[0;32m    LogRateLimitBurst: '0'[0m
[0;32m    LogRateLimitIntervalUSec: '0'[0m
[0;32m    LogsDirectoryMode: '0755'[0m
[0;32m    MainPID: '12549'[0m
[0;32m    ManagedOOMMemoryPressure: auto[0m
[0;32m    ManagedOOMMemoryPressureLimit: '0'[0m
[0;32m    ManagedOOMPreference: none[0m
[0;32m    ManagedOOMSwap: auto[0m
[0;32m    MemoryAccounting: 'yes'[0m
[0;32m    MemoryAvailable: infinity[0m
[0;32m    MemoryCurrent: '*********'[0m
[0;32m    MemoryDenyWriteExecute: 'no'[0m
[0;32m    MemoryHigh: infinity[0m
[0;32m    MemoryLimit: infinity[0m
[0;32m    MemoryLow: '0'[0m
[0;32m    MemoryMax: infinity[0m
[0;32m    MemoryMin: '0'[0m
[0;32m    MemorySwapMax: infinity[0m
[0;32m    MountAPIVFS: 'no'[0m
[0;32m    NFileDescriptorStore: '0'[0m
[0;32m    NRestarts: '0'[0m
[0;32m    NUMAPolicy: n/a[0m
[0;32m    Names: docker.service[0m
[0;32m    NeedDaemonReload: 'no'[0m
[0;32m    Nice: '0'[0m
[0;32m    NoNewPrivileges: 'no'[0m
[0;32m    NonBlocking: 'no'[0m
[0;32m    NotifyAccess: main[0m
[0;32m    OOMPolicy: continue[0m
[0;32m    OOMScoreAdjust: '-500'[0m
[0;32m    OnFailureJobMode: replace[0m
[0;32m    OnSuccessJobMode: fail[0m
[0;32m    Perpetual: 'no'[0m
[0;32m    PrivateDevices: 'no'[0m
[0;32m    PrivateIPC: 'no'[0m
[0;32m    PrivateMounts: 'no'[0m
[0;32m    PrivateNetwork: 'no'[0m
[0;32m    PrivateTmp: 'no'[0m
[0;32m    PrivateUsers: 'no'[0m
[0;32m    ProcSubset: all[0m
[0;32m    ProtectClock: 'no'[0m
[0;32m    ProtectControlGroups: 'no'[0m
[0;32m    ProtectHome: 'no'[0m
[0;32m    ProtectHostname: 'no'[0m
[0;32m    ProtectKernelLogs: 'no'[0m
[0;32m    ProtectKernelModules: 'no'[0m
[0;32m    ProtectKernelTunables: 'no'[0m
[0;32m    ProtectProc: default[0m
[0;32m    ProtectSystem: 'no'[0m
[0;32m    RefuseManualStart: 'no'[0m
[0;32m    RefuseManualStop: 'no'[0m
[0;32m    ReloadResult: success[0m
[0;32m    RemainAfterExit: 'no'[0m
[0;32m    RemoveIPC: 'no'[0m
[0;32m    Requires: system.slice docker.socket sysinit.target[0m
[0;32m    Restart: always[0m
[0;32m    RestartKillSignal: '15'[0m
[0;32m    RestartUSec: 2s[0m
[0;32m    RestrictNamespaces: 'no'[0m
[0;32m    RestrictRealtime: 'no'[0m
[0;32m    RestrictSUIDSGID: 'no'[0m
[0;32m    Result: success[0m
[0;32m    RootDirectoryStartOnly: 'no'[0m
[0;32m    RuntimeDirectoryMode: '0755'[0m
[0;32m    RuntimeDirectoryPreserve: 'no'[0m
[0;32m    RuntimeMaxUSec: infinity[0m
[0;32m    SameProcessGroup: 'no'[0m
[0;32m    SecureBits: '0'[0m
[0;32m    SendSIGHUP: 'no'[0m
[0;32m    SendSIGKILL: 'yes'[0m
[0;32m    Slice: system.slice[0m
[0;32m    StandardError: inherit[0m
[0;32m    StandardInput: 'null'[0m
[0;32m    StandardOutput: journal[0m
[0;32m    StartLimitAction: none[0m
[0;32m    StartLimitBurst: '3'[0m
[0;32m    StartLimitIntervalUSec: 1min[0m
[0;32m    StartupBlockIOWeight: '[not set]'[0m
[0;32m    StartupCPUShares: '[not set]'[0m
[0;32m    StartupCPUWeight: '[not set]'[0m
[0;32m    StartupIOWeight: '[not set]'[0m
[0;32m    StateChangeTimestamp: Thu 2025-08-21 11:39:32 UTC[0m
[0;32m    StateChangeTimestampMonotonic: '**********'[0m
[0;32m    StateDirectoryMode: '0755'[0m
[0;32m    StatusErrno: '0'[0m
[0;32m    StopWhenUnneeded: 'no'[0m
[0;32m    SubState: running[0m
[0;32m    SuccessAction: none[0m
[0;32m    SyslogFacility: '3'[0m
[0;32m    SyslogLevel: '6'[0m
[0;32m    SyslogLevelPrefix: 'yes'[0m
[0;32m    SyslogPriority: '30'[0m
[0;32m    SystemCallErrorNumber: '**********'[0m
[0;32m    TTYReset: 'no'[0m
[0;32m    TTYVHangup: 'no'[0m
[0;32m    TTYVTDisallocate: 'no'[0m
[0;32m    TasksAccounting: 'yes'[0m
[0;32m    TasksCurrent: '9'[0m
[0;32m    TasksMax: infinity[0m
[0;32m    TimeoutAbortUSec: 1min 30s[0m
[0;32m    TimeoutCleanUSec: infinity[0m
[0;32m    TimeoutStartFailureMode: terminate[0m
[0;32m    TimeoutStartUSec: infinity[0m
[0;32m    TimeoutStopFailureMode: terminate[0m
[0;32m    TimeoutStopUSec: 1min 30s[0m
[0;32m    TimerSlackNSec: '50000'[0m
[0;32m    Transient: 'no'[0m
[0;32m    TriggeredBy: docker.socket[0m
[0;32m    Type: notify[0m
[0;32m    UID: '[not set]'[0m
[0;32m    UMask: '0022'[0m
[0;32m    UnitFilePreset: enabled[0m
[0;32m    UnitFileState: enabled[0m
[0;32m    UtmpMode: init[0m
[0;32m    WantedBy: multi-user.target[0m
[0;32m    Wants: containerd.service network-online.target[0m
[0;32m    WatchdogSignal: '6'[0m
[0;32m    WatchdogTimestamp: n/a[0m
[0;32m    WatchdogTimestampMonotonic: '0'[0m
[0;32m    WatchdogUSec: '0'[0m

TASK [Add users to docker group] ***********************************************
[0;33mchanged: [web_server] => (item=ubuntu) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  append: true[0m
[0;33m  comment: Ubuntu[0m
[0;33m  group: 1000[0m
[0;33m  groups: docker[0m
[0;33m  home: /home/<USER>
[0;33m  item: ubuntu[0m
[0;33m  move_home: false[0m
[0;33m  name: ubuntu[0m
[0;33m  shell: /bin/bash[0m
[0;33m  state: present[0m
[0;33m  uid: 1000[0m
[0;33mchanged: [web_server] => (item=deploy) => changed=true [0m
[0;33m  ansible_loop_var: item[0m
[0;33m  append: true[0m
[0;33m  comment: ''[0m
[0;33m  group: 1001[0m
[0;33m  groups: docker[0m
[0;33m  home: /home/<USER>
[0;33m  item: deploy[0m
[0;33m  move_home: false[0m
[0;33m  name: deploy[0m
[0;33m  shell: /bin/bash[0m
[0;33m  state: present[0m
[0;33m  uid: 1001[0m

TASK [Install Docker Compose] **************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum_dest: null[0m
[0;33m  checksum_src: e2bd7843ba0536e88b6e9c4284148bbb8dc14d1e[0m
[0;33m  dest: /usr/local/bin/docker-compose[0m
[0;33m  elapsed: 0[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: c0311182de07ba35e54eff39af250b03[0m
[0;33m  mode: '0755'[0m
[0;33m  msg: OK (59542867 bytes)[0m
[0;33m  owner: root[0m
[0;33m  size: 59542867[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776398.861138-28727-105604923288219/tmpqi2vqk_2[0m
[0;33m  state: file[0m
[0;33m  status_code: 200[0m
[0;33m  uid: 0[0m
[0;33m  url: https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-linux-x86_64[0m

TASK [Create docker-compose symlink] *******************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  dest: /usr/bin/docker-compose[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  mode: '0777'[0m
[0;33m  owner: root[0m
[0;33m  size: 29[0m
[0;33m  src: /usr/local/bin/docker-compose[0m
[0;33m  state: link[0m
[0;33m  uid: 0[0m

TASK [Install NGINX] ***********************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  cache_update_time: 1755776343[0m
[0;33m  cache_updated: false[0m
[0;33m  stderr: ''[0m
[0;33m  stderr_lines: <omitted>[0m
[0;33m  stdout: |-[0m
[0;33m    Reading package lists...[0m
[0;33m    Building dependency tree...[0m
[0;33m    Reading state information...[0m
[0;33m    The following additional packages will be installed:[0m
[0;33m      libnginx-mod-http-geoip2 libnginx-mod-http-image-filter[0m
[0;33m      libnginx-mod-http-xslt-filter libnginx-mod-mail libnginx-mod-stream[0m
[0;33m      libnginx-mod-stream-geoip2 nginx-common nginx-core[0m
[0;33m    Suggested packages:[0m
[0;33m      fcgiwrap nginx-doc ssl-cert[0m
[0;33m    The following NEW packages will be installed:[0m
[0;33m      libnginx-mod-http-geoip2 libnginx-mod-http-image-filter[0m
[0;33m      libnginx-mod-http-xslt-filter libnginx-mod-mail libnginx-mod-stream[0m
[0;33m      libnginx-mod-stream-geoip2 nginx nginx-common nginx-core[0m
[0;33m    0 upgraded, 9 newly installed, 0 to remove and 0 not upgraded.[0m
[0;33m    Need to get 697 kB of archives.[0m
[0;33m    After this operation, 2395 kB of additional disk space will be used.[0m
[0;33m    Get:1 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 nginx-common all 1.18.0-6ubuntu14.6 [40.1 kB][0m
[0;33m    Get:2 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-http-geoip2 amd64 1.18.0-6ubuntu14.6 [12.0 kB][0m
[0;33m    Get:3 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-http-image-filter amd64 1.18.0-6ubuntu14.6 [15.5 kB][0m
[0;33m    Get:4 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-http-xslt-filter amd64 1.18.0-6ubuntu14.6 [13.8 kB][0m
[0;33m    Get:5 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-mail amd64 1.18.0-6ubuntu14.6 [45.8 kB][0m
[0;33m    Get:6 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-stream amd64 1.18.0-6ubuntu14.6 [73.0 kB][0m
[0;33m    Get:7 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 libnginx-mod-stream-geoip2 amd64 1.18.0-6ubuntu14.6 [10.1 kB][0m
[0;33m    Get:8 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 nginx-core amd64 1.18.0-6ubuntu14.6 [483 kB][0m
[0;33m    Get:9 http://us-west-2.ec2.archive.ubuntu.com/ubuntu jammy-updates/main amd64 nginx amd64 1.18.0-6ubuntu14.6 [3882 B][0m
[0;33m    Preconfiguring packages ...[0m
[0;33m    Fetched 697 kB in 0s (15.3 MB/s)[0m
[0;33m    Selecting previously unselected package nginx-common.[0m
[0;33m    (Reading database ... (Reading database ... 5%(Reading database ... 10%(Reading database ... 15%(Reading database ... 20%(Reading database ... 25%(Reading database ... 30%(Reading database ... 35%(Reading database ... 40%(Reading database ... 45%(Reading database ... 50%(Reading database ... 55%(Reading database ... 60%(Reading database ... 65%(Reading database ... 70%(Reading database ... 75%(Reading database ... 80%(Reading database ... 85%(Reading database ... 90%(Reading database ... 95%(Reading database ... 100%(Reading database ... 104137 files and directories currently installed.)[0m
[0;33m    Preparing to unpack .../0-nginx-common_1.18.0-6ubuntu14.6_all.deb ...[0m
[0;33m    Unpacking nginx-common (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-http-geoip2.[0m
[0;33m    Preparing to unpack .../1-libnginx-mod-http-geoip2_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-http-geoip2 (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-http-image-filter.[0m
[0;33m    Preparing to unpack .../2-libnginx-mod-http-image-filter_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-http-image-filter (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-http-xslt-filter.[0m
[0;33m    Preparing to unpack .../3-libnginx-mod-http-xslt-filter_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-http-xslt-filter (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-mail.[0m
[0;33m    Preparing to unpack .../4-libnginx-mod-mail_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-mail (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-stream.[0m
[0;33m    Preparing to unpack .../5-libnginx-mod-stream_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-stream (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package libnginx-mod-stream-geoip2.[0m
[0;33m    Preparing to unpack .../6-libnginx-mod-stream-geoip2_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking libnginx-mod-stream-geoip2 (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package nginx-core.[0m
[0;33m    Preparing to unpack .../7-nginx-core_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking nginx-core (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Selecting previously unselected package nginx.[0m
[0;33m    Preparing to unpack .../8-nginx_1.18.0-6ubuntu14.6_amd64.deb ...[0m
[0;33m    Unpacking nginx (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up nginx-common (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Created symlink /etc/systemd/system/multi-user.target.wants/nginx.service → /lib/systemd/system/nginx.service.[0m
[0;33m    Setting up libnginx-mod-http-xslt-filter (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up libnginx-mod-http-geoip2 (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up libnginx-mod-mail (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up libnginx-mod-http-image-filter (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up libnginx-mod-stream (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up libnginx-mod-stream-geoip2 (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Setting up nginx-core (1.18.0-6ubuntu14.6) ...[0m
[0;33m     * Upgrading binary nginx       [80G [74G[ OK ][0m
[0;33m    Setting up nginx (1.18.0-6ubuntu14.6) ...[0m
[0;33m    Processing triggers for man-db (2.10.2-1) ...[0m
[0;33m    Processing triggers for ufw (0.36.1-4ubuntu0.1) ...[0m
[0;33m    NEEDRESTART-VER: 3.5[0m
[0;33m    NEEDRESTART-KCUR: 6.8.0-1033-aws[0m
[0;33m    NEEDRESTART-KEXP: 6.8.0-1035-aws[0m
[0;33m    NEEDRESTART-KSTA: 3[0m
[0;33m    NEEDRESTART-SVC: irqbalance.service[0m
[0;33m    NEEDRESTART-SVC: multipathd.service[0m
[0;33m    NEEDRESTART-SVC: networkd-dispatcher.service[0m
[0;33m    NEEDRESTART-SVC: packagekit.service[0m
[0;33m    NEEDRESTART-SVC: polkit.service[0m
[0;33m    NEEDRESTART-SVC: unattended-upgrades.service[0m
[0;33m  stdout_lines: <omitted>[0m

TASK [Start and enable NGINX service] ******************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  enabled: true[0m
[0;32m  name: nginx[0m
[0;32m  state: started[0m
[0;32m  status:[0m
[0;32m    ActiveEnterTimestamp: Thu 2025-08-21 11:40:21 UTC[0m
[0;32m    ActiveEnterTimestampMonotonic: '**********'[0m
[0;32m    ActiveExitTimestamp: n/a[0m
[0;32m    ActiveExitTimestampMonotonic: '0'[0m
[0;32m    ActiveState: active[0m
[0;32m    After: nss-lookup.target system.slice network.target sysinit.target basic.target systemd-journald.socket[0m
[0;32m    AllowIsolate: 'no'[0m
[0;32m    AssertResult: 'yes'[0m
[0;32m    AssertTimestamp: Thu 2025-08-21 11:40:21 UTC[0m
[0;32m    AssertTimestampMonotonic: '**********'[0m
[0;32m    Before: multi-user.target shutdown.target[0m
[0;32m    BlockIOAccounting: 'no'[0m
[0;32m    BlockIOWeight: '[not set]'[0m
[0;32m    CPUAccounting: 'yes'[0m
[0;32m    CPUAffinityFromNUMA: 'no'[0m
[0;32m    CPUQuotaPerSecUSec: infinity[0m
[0;32m    CPUQuotaPeriodUSec: infinity[0m
[0;32m    CPUSchedulingPolicy: '0'[0m
[0;32m    CPUSchedulingPriority: '0'[0m
[0;32m    CPUSchedulingResetOnFork: 'no'[0m
[0;32m    CPUShares: '[not set]'[0m
[0;32m    CPUUsageNSec: '********'[0m
[0;32m    CPUWeight: '[not set]'[0m
[0;32m    CacheDirectoryMode: '0755'[0m
[0;32m    CanFreeze: 'yes'[0m
[0;32m    CanIsolate: 'no'[0m
[0;32m    CanReload: 'yes'[0m
[0;32m    CanStart: 'yes'[0m
[0;32m    CanStop: 'yes'[0m
[0;32m    CapabilityBoundingSet: cap_chown cap_dac_override cap_dac_read_search cap_fowner cap_fsetid cap_kill cap_setgid cap_setuid cap_setpcap cap_linux_immutable cap_net_bind_service cap_net_broadcast cap_net_admin cap_net_raw cap_ipc_lock cap_ipc_owner cap_sys_module cap_sys_rawio cap_sys_chroot cap_sys_ptrace cap_sys_pacct cap_sys_admin cap_sys_boot cap_sys_nice cap_sys_resource cap_sys_time cap_sys_tty_config cap_mknod cap_lease cap_audit_write cap_audit_control cap_setfcap cap_mac_override cap_mac_admin cap_syslog cap_wake_alarm cap_block_suspend cap_audit_read cap_perfmon cap_bpf cap_checkpoint_restore[0m
[0;32m    CleanResult: success[0m
[0;32m    CollectMode: inactive[0m
[0;32m    ConditionResult: 'yes'[0m
[0;32m    ConditionTimestamp: Thu 2025-08-21 11:40:21 UTC[0m
[0;32m    ConditionTimestampMonotonic: '1084481051'[0m
[0;32m    ConfigurationDirectoryMode: '0755'[0m
[0;32m    Conflicts: shutdown.target[0m
[0;32m    ControlGroup: /system.slice/nginx.service[0m
[0;32m    ControlPID: '0'[0m
[0;32m    CoredumpFilter: '0x33'[0m
[0;32m    DefaultDependencies: 'yes'[0m
[0;32m    DefaultMemoryLow: '0'[0m
[0;32m    DefaultMemoryMin: '0'[0m
[0;32m    Delegate: 'no'[0m
[0;32m    Description: A high performance web server and a reverse proxy server[0m
[0;32m    DevicePolicy: auto[0m
[0;32m    Documentation: '"man:nginx(8)"'[0m
[0;32m    DynamicUser: 'no'[0m
[0;32m    EffectiveCPUs: 0-1[0m
[0;32m    EffectiveMemoryNodes: '0'[0m
[0;32m    ExecMainCode: '0'[0m
[0;32m    ExecMainExitTimestamp: n/a[0m
[0;32m    ExecMainExitTimestampMonotonic: '0'[0m
[0;32m    ExecMainPID: '13313'[0m
[0;32m    ExecMainStartTimestamp: Thu 2025-08-21 11:40:22 UTC[0m
[0;32m    ExecMainStartTimestampMonotonic: '**********'[0m
[0;32m    ExecMainStatus: '0'[0m
[0;32m    ExecReload: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -g daemon on; master_process on; -s reload ; ignore_errors=no ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecReloadEx: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -g daemon on; master_process on; -s reload ; flags= ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecStart: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -g daemon on; master_process on; ; ignore_errors=no ; start_time=[Thu 2025-08-21 11:40:21 UTC] ; stop_time=[Thu 2025-08-21 11:40:21 UTC] ; pid=13222 ; code=exited ; status=0 }'[0m
[0;32m    ExecStartEx: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -g daemon on; master_process on; ; flags= ; start_time=[Thu 2025-08-21 11:40:21 UTC] ; stop_time=[Thu 2025-08-21 11:40:21 UTC] ; pid=13222 ; code=exited ; status=0 }'[0m
[0;32m    ExecStartPre: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -t -q -g daemon on; master_process on; ; ignore_errors=no ; start_time=[Thu 2025-08-21 11:40:21 UTC] ; stop_time=[Thu 2025-08-21 11:40:21 UTC] ; pid=13221 ; code=exited ; status=0 }'[0m
[0;32m    ExecStartPreEx: '{ path=/usr/sbin/nginx ; argv[]=/usr/sbin/nginx -t -q -g daemon on; master_process on; ; flags= ; start_time=[Thu 2025-08-21 11:40:21 UTC] ; stop_time=[Thu 2025-08-21 11:40:21 UTC] ; pid=13221 ; code=exited ; status=0 }'[0m
[0;32m    ExecStop: '{ path=/sbin/start-stop-daemon ; argv[]=/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile /run/nginx.pid ; ignore_errors=yes ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    ExecStopEx: '{ path=/sbin/start-stop-daemon ; argv[]=/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile /run/nginx.pid ; flags=ignore-failure ; start_time=[n/a] ; stop_time=[n/a] ; pid=0 ; code=(null) ; status=0/0 }'[0m
[0;32m    FailureAction: none[0m
[0;32m    FileDescriptorStoreMax: '0'[0m
[0;32m    FinalKillSignal: '9'[0m
[0;32m    FragmentPath: /lib/systemd/system/nginx.service[0m
[0;32m    FreezerState: running[0m
[0;32m    GID: '[not set]'[0m
[0;32m    GuessMainPID: 'yes'[0m
[0;32m    IOAccounting: 'no'[0m
[0;32m    IOReadBytes: '18446744073709551615'[0m
[0;32m    IOReadOperations: '18446744073709551615'[0m
[0;32m    IOSchedulingClass: '2'[0m
[0;32m    IOSchedulingPriority: '4'[0m
[0;32m    IOWeight: '[not set]'[0m
[0;32m    IOWriteBytes: '18446744073709551615'[0m
[0;32m    IOWriteOperations: '18446744073709551615'[0m
[0;32m    IPAccounting: 'no'[0m
[0;32m    IPEgressBytes: '[no data]'[0m
[0;32m    IPEgressPackets: '[no data]'[0m
[0;32m    IPIngressBytes: '[no data]'[0m
[0;32m    IPIngressPackets: '[no data]'[0m
[0;32m    Id: nginx.service[0m
[0;32m    IgnoreOnIsolate: 'no'[0m
[0;32m    IgnoreSIGPIPE: 'yes'[0m
[0;32m    InactiveEnterTimestamp: n/a[0m
[0;32m    InactiveEnterTimestampMonotonic: '0'[0m
[0;32m    InactiveExitTimestamp: Thu 2025-08-21 11:40:21 UTC[0m
[0;32m    InactiveExitTimestampMonotonic: '**********'[0m
[0;32m    InvocationID: 03d86fdcb60d46238ad1e4db4a25d447[0m
[0;32m    JobRunningTimeoutUSec: infinity[0m
[0;32m    JobTimeoutAction: none[0m
[0;32m    JobTimeoutUSec: infinity[0m
[0;32m    KeyringMode: private[0m
[0;32m    KillMode: mixed[0m
[0;32m    KillSignal: '15'[0m
[0;32m    LimitAS: infinity[0m
[0;32m    LimitASSoft: infinity[0m
[0;32m    LimitCORE: infinity[0m
[0;32m    LimitCORESoft: '0'[0m
[0;32m    LimitCPU: infinity[0m
[0;32m    LimitCPUSoft: infinity[0m
[0;32m    LimitDATA: infinity[0m
[0;32m    LimitDATASoft: infinity[0m
[0;32m    LimitFSIZE: infinity[0m
[0;32m    LimitFSIZESoft: infinity[0m
[0;32m    LimitLOCKS: infinity[0m
[0;32m    LimitLOCKSSoft: infinity[0m
[0;32m    LimitMEMLOCK: '8388608'[0m
[0;32m    LimitMEMLOCKSoft: '8388608'[0m
[0;32m    LimitMSGQUEUE: '819200'[0m
[0;32m    LimitMSGQUEUESoft: '819200'[0m
[0;32m    LimitNICE: '0'[0m
[0;32m    LimitNICESoft: '0'[0m
[0;32m    LimitNOFILE: '524288'[0m
[0;32m    LimitNOFILESoft: '1024'[0m
[0;32m    LimitNPROC: '3577'[0m
[0;32m    LimitNPROCSoft: '3577'[0m
[0;32m    LimitRSS: infinity[0m
[0;32m    LimitRSSSoft: infinity[0m
[0;32m    LimitRTPRIO: '0'[0m
[0;32m    LimitRTPRIOSoft: '0'[0m
[0;32m    LimitRTTIME: infinity[0m
[0;32m    LimitRTTIMESoft: infinity[0m
[0;32m    LimitSIGPENDING: '3577'[0m
[0;32m    LimitSIGPENDINGSoft: '3577'[0m
[0;32m    LimitSTACK: infinity[0m
[0;32m    LimitSTACKSoft: '8388608'[0m
[0;32m    LoadState: loaded[0m
[0;32m    LockPersonality: 'no'[0m
[0;32m    LogLevelMax: '-1'[0m
[0;32m    LogRateLimitBurst: '0'[0m
[0;32m    LogRateLimitIntervalUSec: '0'[0m
[0;32m    LogsDirectoryMode: '0755'[0m
[0;32m    MainPID: '13313'[0m
[0;32m    ManagedOOMMemoryPressure: auto[0m
[0;32m    ManagedOOMMemoryPressureLimit: '0'[0m
[0;32m    ManagedOOMPreference: none[0m
[0;32m    ManagedOOMSwap: auto[0m
[0;32m    MemoryAccounting: 'yes'[0m
[0;32m    MemoryAvailable: infinity[0m
[0;32m    MemoryCurrent: '9445376'[0m
[0;32m    MemoryDenyWriteExecute: 'no'[0m
[0;32m    MemoryHigh: infinity[0m
[0;32m    MemoryLimit: infinity[0m
[0;32m    MemoryLow: '0'[0m
[0;32m    MemoryMax: infinity[0m
[0;32m    MemoryMin: '0'[0m
[0;32m    MemorySwapMax: infinity[0m
[0;32m    MountAPIVFS: 'no'[0m
[0;32m    NFileDescriptorStore: '0'[0m
[0;32m    NRestarts: '0'[0m
[0;32m    NUMAPolicy: n/a[0m
[0;32m    Names: nginx.service[0m
[0;32m    NeedDaemonReload: 'no'[0m
[0;32m    Nice: '0'[0m
[0;32m    NoNewPrivileges: 'no'[0m
[0;32m    NonBlocking: 'no'[0m
[0;32m    NotifyAccess: none[0m
[0;32m    OOMPolicy: stop[0m
[0;32m    OOMScoreAdjust: '0'[0m
[0;32m    OnFailureJobMode: replace[0m
[0;32m    OnSuccessJobMode: fail[0m
[0;32m    PIDFile: /run/nginx.pid[0m
[0;32m    Perpetual: 'no'[0m
[0;32m    PrivateDevices: 'no'[0m
[0;32m    PrivateIPC: 'no'[0m
[0;32m    PrivateMounts: 'no'[0m
[0;32m    PrivateNetwork: 'no'[0m
[0;32m    PrivateTmp: 'no'[0m
[0;32m    PrivateUsers: 'no'[0m
[0;32m    ProcSubset: all[0m
[0;32m    ProtectClock: 'no'[0m
[0;32m    ProtectControlGroups: 'no'[0m
[0;32m    ProtectHome: 'no'[0m
[0;32m    ProtectHostname: 'no'[0m
[0;32m    ProtectKernelLogs: 'no'[0m
[0;32m    ProtectKernelModules: 'no'[0m
[0;32m    ProtectKernelTunables: 'no'[0m
[0;32m    ProtectProc: default[0m
[0;32m    ProtectSystem: 'no'[0m
[0;32m    RefuseManualStart: 'no'[0m
[0;32m    RefuseManualStop: 'no'[0m
[0;32m    ReloadResult: success[0m
[0;32m    RemainAfterExit: 'no'[0m
[0;32m    RemoveIPC: 'no'[0m
[0;32m    Requires: sysinit.target system.slice[0m
[0;32m    Restart: 'no'[0m
[0;32m    RestartKillSignal: '15'[0m
[0;32m    RestartUSec: 100ms[0m
[0;32m    RestrictNamespaces: 'no'[0m
[0;32m    RestrictRealtime: 'no'[0m
[0;32m    RestrictSUIDSGID: 'no'[0m
[0;32m    Result: success[0m
[0;32m    RootDirectoryStartOnly: 'no'[0m
[0;32m    RuntimeDirectoryMode: '0755'[0m
[0;32m    RuntimeDirectoryPreserve: 'no'[0m
[0;32m    RuntimeMaxUSec: infinity[0m
[0;32m    SameProcessGroup: 'no'[0m
[0;32m    SecureBits: '0'[0m
[0;32m    SendSIGHUP: 'no'[0m
[0;32m    SendSIGKILL: 'yes'[0m
[0;32m    Slice: system.slice[0m
[0;32m    StandardError: inherit[0m
[0;32m    StandardInput: 'null'[0m
[0;32m    StandardOutput: journal[0m
[0;32m    StartLimitAction: none[0m
[0;32m    StartLimitBurst: '5'[0m
[0;32m    StartLimitIntervalUSec: 10s[0m
[0;32m    StartupBlockIOWeight: '[not set]'[0m
[0;32m    StartupCPUShares: '[not set]'[0m
[0;32m    StartupCPUWeight: '[not set]'[0m
[0;32m    StartupIOWeight: '[not set]'[0m
[0;32m    StateChangeTimestamp: Thu 2025-08-21 11:40:21 UTC[0m
[0;32m    StateChangeTimestampMonotonic: '**********'[0m
[0;32m    StateDirectoryMode: '0755'[0m
[0;32m    StatusErrno: '0'[0m
[0;32m    StopWhenUnneeded: 'no'[0m
[0;32m    SubState: running[0m
[0;32m    SuccessAction: none[0m
[0;32m    SyslogFacility: '3'[0m
[0;32m    SyslogLevel: '6'[0m
[0;32m    SyslogLevelPrefix: 'yes'[0m
[0;32m    SyslogPriority: '30'[0m
[0;32m    SystemCallErrorNumber: '**********'[0m
[0;32m    TTYReset: 'no'[0m
[0;32m    TTYVHangup: 'no'[0m
[0;32m    TTYVTDisallocate: 'no'[0m
[0;32m    TasksAccounting: 'yes'[0m
[0;32m    TasksCurrent: '3'[0m
[0;32m    TasksMax: '1073'[0m
[0;32m    TimeoutAbortUSec: 5s[0m
[0;32m    TimeoutCleanUSec: infinity[0m
[0;32m    TimeoutStartFailureMode: terminate[0m
[0;32m    TimeoutStartUSec: 1min 30s[0m
[0;32m    TimeoutStopFailureMode: terminate[0m
[0;32m    TimeoutStopUSec: 5s[0m
[0;32m    TimerSlackNSec: '50000'[0m
[0;32m    Transient: 'no'[0m
[0;32m    Type: forking[0m
[0;32m    UID: '[not set]'[0m
[0;32m    UMask: '0022'[0m
[0;32m    UnitFilePreset: enabled[0m
[0;32m    UnitFileState: enabled[0m
[0;32m    UtmpMode: init[0m
[0;32m    WantedBy: multi-user.target[0m
[0;32m    WatchdogSignal: '6'[0m
[0;32m    WatchdogTimestamp: n/a[0m
[0;32m    WatchdogTimestampMonotonic: '0'[0m
[0;32m    WatchdogUSec: '0'[0m

TASK [Remove default NGINX site] ***********************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  path: /etc/nginx/sites-enabled/default[0m
[0;33m  state: absent[0m

TASK [Create custom NGINX configuration] ***************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  backup_file: /etc/nginx/sites-available/default.13465.2025-08-21@11:40:54~[0m
[0;33m  checksum: d9f5c73c9f7b14fe91ee1a0efae41504675b6d70[0m
[0;33m  dest: /etc/nginx/sites-available/default[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: 2d47696634ce042c7fb12d424231b130[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 1185[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776441.224064-28786-69333568877687/.source[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Enable default site] *****************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  dest: /etc/nginx/sites-enabled/default[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  mode: '0777'[0m
[0;33m  owner: root[0m
[0;33m  size: 34[0m
[0;33m  src: /etc/nginx/sites-available/default[0m
[0;33m  state: link[0m
[0;33m  uid: 0[0m

TASK [Create custom index page] ************************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: a29e74670c7ae0f78e205cb71e18dc0cc151b1da[0m
[0;33m  dest: /var/www/html/index.html[0m
[0;33m  gid: 0[0m
[0;33m  group: root[0m
[0;33m  md5sum: b4634ed4b30efbedc536da4728acd524[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: root[0m
[0;33m  size: 1746[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776461.9096696-28816-45437625608851/.source.html[0m
[0;33m  state: file[0m
[0;33m  uid: 0[0m

TASK [Create Docker application directory] *************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  mode: '0755'[0m
[0;33m  owner: deploy[0m
[0;33m  path: /opt/docker-apps[0m
[0;33m  size: 4096[0m
[0;33m  state: directory[0m
[0;33m  uid: 1001[0m

TASK [Create sample Docker Compose file] ***************************************
[0;33mchanged: [web_server] => changed=true [0m
[0;33m  checksum: 3aa0f06de8c27dd968171569fa5dab9a5c538443[0m
[0;33m  dest: /opt/docker-apps/docker-compose.yml[0m
[0;33m  gid: 1001[0m
[0;33m  group: deploy[0m
[0;33m  md5sum: e309d7abb3d6cbf3e6ef11d0b553c9eb[0m
[0;33m  mode: '0644'[0m
[0;33m  owner: deploy[0m
[0;33m  size: 570[0m
[0;33m  src: /home/<USER>/.ansible/tmp/ansible-tmp-1755776482.200213-28848-256652730045695/.source.yml[0m
[0;33m  state: file[0m
[0;33m  uid: 1001[0m

TASK [Test Docker installation] ************************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cmd:[0m
[0;32m  - docker[0m
[0;32m  - --version[0m
[0;32m  delta: '0:00:00.203811'[0m
[0;32m  end: '2025-08-21 11:41:41.415465'[0m
[0;32m  msg: ''[0m
[0;32m  rc: 0[0m
[0;32m  start: '2025-08-21 11:41:41.211654'[0m
[0;32m  stderr: ''[0m
[0;32m  stderr_lines: <omitted>[0m
[0;32m  stdout: Docker version 28.3.3, build 980b856[0m
[0;32m  stdout_lines: <omitted>[0m

TASK [Test Docker Compose installation] ****************************************
[0;32mok: [web_server] => changed=false [0m
[0;32m  cmd:[0m
[0;32m  - docker-compose[0m
[0;32m  - --version[0m
[0;32m  delta: '0:00:00.351193'[0m
[0;32m  end: '2025-08-21 11:41:47.974446'[0m
[0;32m  msg: ''[0m
[0;32m  rc: 0[0m
[0;32m  start: '2025-08-21 11:41:47.623253'[0m
[0;32m  stderr: ''[0m
[0;32m  stderr_lines: <omitted>[0m
[0;32m  stdout: Docker Compose version v2.21.0[0m
[0;32m  stdout_lines: <omitted>[0m

TASK [Test NGINX installation] *************************************************
[0;31mfatal: [web_server]: FAILED! => changed=false [0m
[0;31m  cmd:[0m
[0;31m  - nginx[0m
[0;31m  - -t[0m
[0;31m  delta: '0:00:00.018476'[0m
[0;31m  end: '2025-08-21 11:41:54.195257'[0m
[0;31m  msg: non-zero return code[0m
[0;31m  rc: 1[0m
[0;31m  start: '2025-08-21 11:41:54.176781'[0m
[0;31m  stderr: |-[0m
[0;31m    nginx: [emerg] invalid value "must-revalidate" in /etc/nginx/sites-enabled/default:20[0m
[0;31m    nginx: configuration file /etc/nginx/nginx.conf test failed[0m
[0;31m  stderr_lines: <omitted>[0m
[0;31m  stdout: ''[0m
[0;31m  stdout_lines: <omitted>[0m

PLAY RECAP *********************************************************************
[0;31mweb_server[0m                 : [0;32mok=20  [0m [0;33mchanged=13  [0m unreachable=0    [0;31mfailed=1   [0m skipped=0    rescued=0    ignored=0   

