{"ansible_host": {"sensitive": true, "type": ["object", {"ansible_ssh_private_key_file": "string", "ansible_user": "string", "instance_id": "string", "private_ip": "string", "public_ip": "string"}], "value": {"ansible_ssh_private_key_file": "~/.ssh/infraops_key", "ansible_user": "ubuntu", "instance_id": "i-044318410f4e56ffa", "private_ip": "*************", "public_ip": "***********"}}, "infrastructure_info": {"sensitive": false, "type": ["object", {"ansible": ["object", {"inventory": ["object", {"web_servers": ["object", {"hosts": ["object", {"infraops-dev-server": ["object", {"ansible_host": "string", "ansible_ssh_private_key_file": "string", "ansible_user": "string", "instance_id": "string", "private_ip": "string"}]}]}]}]}], "environment": "string", "instances": ["object", {"web_server": ["object", {"az": "string", "id": "string", "private_ip": "string", "public_ip": "string", "type": "string"}]}], "region": "string"}], "value": {"ansible": {"inventory": {"web_servers": {"hosts": {"infraops-dev-server": {"ansible_host": "***********", "ansible_ssh_private_key_file": "~/.ssh/infraops_key", "ansible_user": "ubuntu", "instance_id": "i-044318410f4e56ffa", "private_ip": "*************"}}}}}, "environment": "dev", "instances": {"web_server": {"az": "us-west-2a", "id": "i-044318410f4e56ffa", "private_ip": "*************", "public_ip": "***********", "type": "t3.micro"}}, "region": "us-west-2"}}, "instance_id": {"sensitive": false, "type": "string", "value": "i-044318410f4e56ffa"}, "instance_summary": {"sensitive": false, "type": ["object", {"ami_id": "string", "availability_zone": "string", "created_at": "string", "environment": "string", "instance_id": "string", "instance_type": "string", "name": "string", "private_ip": "string", "project": "string", "public_ip": "string", "region": "string"}], "value": {"ami_id": "ami-0ac098a0168eb72d0", "availability_zone": "us-west-2a", "created_at": "2025-08-21T11:22:38Z", "environment": "dev", "instance_id": "i-044318410f4e56ffa", "instance_type": "t3.micro", "name": "infraops-dev-server", "private_ip": "*************", "project": "infraops", "public_ip": "***********", "region": "us-west-2"}}, "key_pair_name": {"sensitive": false, "type": "string", "value": "infraops-dev-key"}, "private_ip": {"sensitive": false, "type": "string", "value": "*************"}, "public_dns": {"sensitive": false, "type": "string", "value": "ec2-52-34-87-37.us-west-2.compute.amazonaws.com"}, "public_ip": {"sensitive": false, "type": "string", "value": "***********"}, "security_group_id": {"sensitive": false, "type": "string", "value": "sg-0bd3d43491b80527c"}}