-- Database initialization script for InfraOps
-- This script creates the necessary tables for storing execution history and metadata

-- Create execution history table
CREATE TABLE IF NOT EXISTS execution_history (
    id SERIAL PRIMARY KEY,
    execution_id VARCHAR(50) UNIQUE NOT NULL,
    prompt TEXT NOT NULL,
    environment VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    terraform_status VARCHAR(20),
    ansible_status VARCHAR(20),
    infrastructure_data JSONB,
    error_messages TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create infrastructure inventory table
CREATE TABLE IF NOT EXISTS infrastructure_inventory (
    id SERIAL PRIMARY KEY,
    execution_id VARCHAR(50) REFERENCES execution_history(execution_id),
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100) NOT NULL,
    resource_name VARCHAR(100),
    region VARCHAR(20),
    status VARCHAR(20),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create cost tracking table
CREATE TABLE IF NOT EXISTS cost_tracking (
    id SERIAL PRIMARY KEY,
    execution_id VARCHAR(50) REFERENCES execution_history(execution_id),
    resource_type VARCHAR(50) NOT NULL,
    estimated_monthly_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    cost_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_execution_history_execution_id ON execution_history(execution_id);
CREATE INDEX IF NOT EXISTS idx_execution_history_status ON execution_history(status);
CREATE INDEX IF NOT EXISTS idx_execution_history_environment ON execution_history(environment);
CREATE INDEX IF NOT EXISTS idx_infrastructure_inventory_execution_id ON infrastructure_inventory(execution_id);
CREATE INDEX IF NOT EXISTS idx_infrastructure_inventory_resource_type ON infrastructure_inventory(resource_type);
CREATE INDEX IF NOT EXISTS idx_cost_tracking_execution_id ON cost_tracking(execution_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_execution_history_updated_at 
    BEFORE UPDATE ON execution_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_infrastructure_inventory_updated_at 
    BEFORE UPDATE ON infrastructure_inventory 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for development
INSERT INTO execution_history (
    execution_id, 
    prompt, 
    environment, 
    status, 
    start_time, 
    end_time,
    terraform_status,
    ansible_status
) VALUES (
    'sample_20231201_120000',
    'Create a web server with NGINX and Docker',
    'dev',
    'completed',
    '2023-12-01 12:00:00+00',
    '2023-12-01 12:15:00+00',
    'completed',
    'completed'
) ON CONFLICT (execution_id) DO NOTHING;
