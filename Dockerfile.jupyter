# Dockerfile for Jupyter development environment
FROM jupyter/scipy-notebook:latest

USER root

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    unzip \
    gnupg \
    software-properties-common \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# Install Terraform
ARG TERRAFORM_VERSION=1.6.0
RUN wget https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && unzip terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && mv terraform /usr/local/bin/ \
    && rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip

USER $NB_UID

# Install additional Python packages
RUN pip install --no-cache-dir \
    ansible \
    ansible-core \
    boto3 \
    click \
    pyyaml \
    jinja2 \
    requests \
    python-dotenv \
    colorlog \
    rich \
    ipywidgets \
    plotly \
    seaborn

# Install Jupyter extensions
RUN jupyter labextension install @jupyter-widgets/jupyterlab-manager

# Set working directory
WORKDIR /home/<USER>/work

# Copy example notebooks
COPY --chown=$NB_UID:$NB_GID notebooks/ /home/<USER>/work/notebooks/
