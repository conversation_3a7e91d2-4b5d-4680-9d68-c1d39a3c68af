#!/usr/bin/env python3
"""
Advanced prompt parsing utilities for natural language infrastructure requests.
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum


class InfrastructureType(Enum):
    """Types of infrastructure that can be provisioned."""
    WEB_SERVER = "web_server"
    DATABASE_SERVER = "database_server"
    APPLICATION_SERVER = "application_server"
    LOAD_BALANCER = "load_balancer"
    CONTAINER_CLUSTER = "container_cluster"


class CloudProvider(Enum):
    """Supported cloud providers."""
    AWS = "aws"
    AZURE = "azure"
    GCP = "gcp"


@dataclass
class ParsedInfrastructureRequest:
    """Structured representation of a parsed infrastructure request."""
    infrastructure_type: InfrastructureType
    cloud_provider: CloudProvider
    region: str
    environment: str
    instance_size: str
    applications: List[str]
    security_requirements: List[str]
    networking: Dict[str, Any]
    storage: Dict[str, Any]
    monitoring: bool
    backup: bool
    high_availability: bool
    estimated_cost_range: str
    confidence_score: float


class NaturalLanguageParser:
    """Advanced parser for natural language infrastructure requests."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Keyword mappings for different categories
        self.infrastructure_keywords = {
            InfrastructureType.WEB_SERVER: [
                "web server", "website", "nginx", "apache", "http", "https",
                "frontend", "static site", "web app", "web application"
            ],
            InfrastructureType.DATABASE_SERVER: [
                "database", "db", "mysql", "postgres", "postgresql", "mongodb",
                "redis", "data storage", "sql", "nosql"
            ],
            InfrastructureType.APPLICATION_SERVER: [
                "app server", "application", "backend", "api", "microservice",
                "service", "node.js", "python", "java", "go"
            ],
            InfrastructureType.CONTAINER_CLUSTER: [
                "docker", "container", "kubernetes", "k8s", "cluster",
                "orchestration", "microservices"
            ]
        }
        
        self.size_keywords = {
            "micro": ["small", "tiny", "minimal", "basic", "free tier", "t3.micro", "t2.micro"],
            "small": ["small", "light", "development", "dev", "testing", "t3.small"],
            "medium": ["medium", "standard", "staging", "t3.medium", "m5.large"],
            "large": ["large", "production", "prod", "high performance", "m5.xlarge", "c5.large"],
            "xlarge": ["extra large", "enterprise", "heavy", "intensive", "c5.xlarge"]
        }
        
        self.region_keywords = {
            "us-east-1": ["us-east", "virginia", "n. virginia", "east coast"],
            "us-west-2": ["us-west", "oregon", "west coast", "pacific"],
            "eu-west-1": ["eu-west", "ireland", "europe"],
            "ap-southeast-1": ["singapore", "asia", "southeast asia"]
        }
        
        self.application_keywords = {
            "nginx": ["nginx", "web server", "reverse proxy"],
            "docker": ["docker", "container", "containerization"],
            "nodejs": ["node.js", "nodejs", "node", "javascript"],
            "python": ["python", "django", "flask", "fastapi"],
            "database": ["database", "db", "mysql", "postgres", "mongodb"],
            "redis": ["redis", "cache", "caching"],
            "monitoring": ["monitoring", "metrics", "logging", "observability"]
        }
        
        self.security_keywords = [
            "secure", "security", "hardening", "firewall", "ssl", "tls",
            "encryption", "vpn", "private", "compliance", "audit"
        ]
    
    def parse(self, prompt: str, environment: str = "dev") -> ParsedInfrastructureRequest:
        """
        Parse a natural language prompt into structured infrastructure request.
        
        Args:
            prompt: Natural language description of infrastructure needs
            environment: Target environment (dev, staging, prod)
            
        Returns:
            ParsedInfrastructureRequest: Structured representation of the request
        """
        self.logger.info(f"Parsing prompt: {prompt}")
        
        prompt_lower = prompt.lower()
        
        # Determine infrastructure type
        infra_type = self._detect_infrastructure_type(prompt_lower)
        
        # Determine cloud provider (default to AWS for now)
        cloud_provider = CloudProvider.AWS
        
        # Determine region
        region = self._detect_region(prompt_lower)
        
        # Determine instance size
        instance_size = self._detect_instance_size(prompt_lower, environment)
        
        # Extract applications
        applications = self._extract_applications(prompt_lower)
        
        # Determine security requirements
        security_requirements = self._extract_security_requirements(prompt_lower, environment)
        
        # Configure networking
        networking = self._configure_networking(prompt_lower, environment)
        
        # Configure storage
        storage = self._configure_storage(prompt_lower, environment, instance_size)
        
        # Determine additional features
        monitoring = self._should_enable_monitoring(prompt_lower, environment)
        backup = self._should_enable_backup(prompt_lower, environment)
        high_availability = self._should_enable_ha(prompt_lower, environment)
        
        # Estimate cost
        cost_range = self._estimate_cost(instance_size, applications, environment)
        
        # Calculate confidence score
        confidence = self._calculate_confidence_score(prompt, infra_type, applications)
        
        return ParsedInfrastructureRequest(
            infrastructure_type=infra_type,
            cloud_provider=cloud_provider,
            region=region,
            environment=environment,
            instance_size=instance_size,
            applications=applications,
            security_requirements=security_requirements,
            networking=networking,
            storage=storage,
            monitoring=monitoring,
            backup=backup,
            high_availability=high_availability,
            estimated_cost_range=cost_range,
            confidence_score=confidence
        )
    
    def _detect_infrastructure_type(self, prompt: str) -> InfrastructureType:
        """Detect the type of infrastructure from the prompt."""
        scores = {}
        
        for infra_type, keywords in self.infrastructure_keywords.items():
            score = sum(1 for keyword in keywords if keyword in prompt)
            if score > 0:
                scores[infra_type] = score
        
        if scores:
            return max(scores, key=scores.get)
        
        # Default to web server if unclear
        return InfrastructureType.WEB_SERVER
    
    def _detect_region(self, prompt: str) -> str:
        """Detect AWS region from the prompt."""
        for region, keywords in self.region_keywords.items():
            if any(keyword in prompt for keyword in keywords):
                return region
        
        # Default region
        return "us-west-2"
    
    def _detect_instance_size(self, prompt: str, environment: str) -> str:
        """Detect appropriate instance size."""
        for size, keywords in self.size_keywords.items():
            if any(keyword in prompt for keyword in keywords):
                return self._map_size_to_instance_type(size)
        
        # Default based on environment
        if environment == "prod":
            return "t3.large"
        elif environment == "staging":
            return "t3.small"
        else:
            return "t3.micro"
    
    def _map_size_to_instance_type(self, size: str) -> str:
        """Map size category to AWS instance type."""
        mapping = {
            "micro": "t3.micro",
            "small": "t3.small",
            "medium": "t3.medium",
            "large": "t3.large",
            "xlarge": "t3.xlarge"
        }
        return mapping.get(size, "t3.micro")
    
    def _extract_applications(self, prompt: str) -> List[str]:
        """Extract applications to install from the prompt."""
        applications = []
        
        for app, keywords in self.application_keywords.items():
            if any(keyword in prompt for keyword in keywords):
                applications.append(app)
        
        # Default applications for web server
        if not applications and any(word in prompt for word in ["web", "server", "site"]):
            applications = ["nginx"]
        
        return applications
    
    def _extract_security_requirements(self, prompt: str, environment: str) -> List[str]:
        """Extract security requirements from the prompt."""
        requirements = ["ssh_hardening", "firewall"]  # Always include basics
        
        if any(keyword in prompt for keyword in self.security_keywords):
            requirements.extend(["fail2ban", "ssl_cert", "log_monitoring"])
        
        if environment == "prod":
            requirements.extend(["backup", "monitoring", "compliance_logging"])
        
        return list(set(requirements))  # Remove duplicates
    
    def _configure_networking(self, prompt: str, environment: str) -> Dict[str, Any]:
        """Configure networking based on prompt and environment."""
        config = {
            "vpc": "default",
            "subnet": "public",
            "public_ip": True,
            "security_groups": ["web", "ssh"]
        }
        
        if "private" in prompt or environment == "prod":
            config["subnet"] = "private"
            config["nat_gateway"] = True
        
        if "load balancer" in prompt or "lb" in prompt:
            config["load_balancer"] = True
        
        return config
    
    def _configure_storage(self, prompt: str, environment: str, instance_size: str) -> Dict[str, Any]:
        """Configure storage based on requirements."""
        # Base storage size based on instance size
        size_mapping = {
            "t3.micro": 20,
            "t3.small": 30,
            "t3.medium": 40,
            "t3.large": 50,
            "t3.xlarge": 100
        }
        
        base_size = size_mapping.get(instance_size, 20)
        
        config = {
            "root_volume_size": base_size,
            "root_volume_type": "gp3",
            "encrypted": True
        }
        
        if "database" in prompt or "storage" in prompt:
            config["additional_volume"] = True
            config["additional_volume_size"] = base_size * 2
        
        if environment == "prod":
            config["backup"] = True
            config["snapshot_schedule"] = "daily"
        
        return config
    
    def _should_enable_monitoring(self, prompt: str, environment: str) -> bool:
        """Determine if monitoring should be enabled."""
        if "monitoring" in prompt or "metrics" in prompt:
            return True
        
        return environment in ["staging", "prod"]
    
    def _should_enable_backup(self, prompt: str, environment: str) -> bool:
        """Determine if backup should be enabled."""
        if "backup" in prompt or "disaster recovery" in prompt:
            return True
        
        return environment == "prod"
    
    def _should_enable_ha(self, prompt: str, environment: str) -> bool:
        """Determine if high availability should be enabled."""
        ha_keywords = ["high availability", "ha", "redundant", "failover", "multi-az"]
        
        if any(keyword in prompt for keyword in ha_keywords):
            return True
        
        return environment == "prod"
    
    def _estimate_cost(self, instance_size: str, applications: List[str], environment: str) -> str:
        """Estimate monthly cost range."""
        base_costs = {
            "t3.micro": 8,
            "t3.small": 16,
            "t3.medium": 33,
            "t3.large": 66,
            "t3.xlarge": 133
        }
        
        base_cost = base_costs.get(instance_size, 8)
        
        # Add costs for additional services
        if "database" in applications:
            base_cost += 15
        
        if "monitoring" in applications:
            base_cost += 5
        
        if environment == "prod":
            base_cost *= 1.5  # Additional costs for prod features
        
        return f"${int(base_cost)}-{int(base_cost * 1.3)}/month"
    
    def _calculate_confidence_score(self, prompt: str, infra_type: InfrastructureType, 
                                  applications: List[str]) -> float:
        """Calculate confidence score for the parsing result."""
        score = 0.5  # Base score
        
        # Increase score based on specific keywords found
        if len(applications) > 0:
            score += 0.2
        
        # Check for specific infrastructure type keywords
        type_keywords = self.infrastructure_keywords.get(infra_type, [])
        matches = sum(1 for keyword in type_keywords if keyword in prompt.lower())
        score += min(matches * 0.1, 0.3)
        
        return min(score, 1.0)
    
    def to_terraform_vars(self, request: ParsedInfrastructureRequest) -> Dict[str, Any]:
        """Convert parsed request to Terraform variables."""
        return {
            "region": request.region,
            "environment": request.environment,
            "instance_type": request.instance_size,
            "instance_name": f"infraops-{request.environment}-server",
            "root_volume_size": request.storage["root_volume_size"],
            "root_volume_type": request.storage["root_volume_type"],
            "enable_detailed_monitoring": request.monitoring,
            "enable_termination_protection": request.environment == "prod"
        }
    
    def to_ansible_vars(self, request: ParsedInfrastructureRequest) -> Dict[str, Any]:
        """Convert parsed request to Ansible variables."""
        return {
            "applications_to_install": request.applications,
            "security_hardening": True,
            "environment": request.environment,
            "backup_enabled": request.backup,
            "monitoring_enabled": request.monitoring
        }
