#!/usr/bin/env python3
"""
Utility functions for InfraOps pipeline.
"""

import os
import json
import logging
import colorlog
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional


def setup_logging(level: int = logging.INFO) -> None:
    """Setup colored logging for the pipeline."""
    
    # Create a color formatter
    formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)


def create_artifacts_dir(base_artifacts_dir: Path, execution_id: str) -> Path:
    """Create artifacts directory for a specific execution."""
    artifacts_dir = base_artifacts_dir / execution_id
    artifacts_dir.mkdir(parents=True, exist_ok=True)
    
    # Create subdirectories
    (artifacts_dir / 'terraform').mkdir(exist_ok=True)
    (artifacts_dir / 'ansible').mkdir(exist_ok=True)
    (artifacts_dir / 'reports').mkdir(exist_ok=True)
    
    return artifacts_dir


def save_json_file(data: Dict[str, Any], file_path: Path) -> bool:
    """Save data to a JSON file."""
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        return True
    except Exception as e:
        logging.error(f"Failed to save JSON file {file_path}: {str(e)}")
        return False


def load_json_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """Load data from a JSON file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Failed to load JSON file {file_path}: {str(e)}")
        return None


def generate_report(execution_data: Dict[str, Any], output_path: Path) -> bool:
    """Generate a comprehensive execution report."""
    try:
        # Generate HTML report
        html_content = _generate_html_report(execution_data)
        html_path = output_path / 'report.html'
        
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        # Generate markdown report
        md_content = _generate_markdown_report(execution_data)
        md_path = output_path / 'report.md'
        
        with open(md_path, 'w') as f:
            f.write(md_content)
        
        logging.info(f"Reports generated: {html_path}, {md_path}")
        return True
        
    except Exception as e:
        logging.error(f"Failed to generate report: {str(e)}")
        return False


def _generate_html_report(data: Dict[str, Any]) -> str:
    """Generate HTML report content."""
    execution_id = data.get('execution_id', 'unknown')
    status = data.get('status', 'unknown')
    start_time = data.get('start_time', 'unknown')
    end_time = data.get('end_time', 'unknown')
    
    # Status color
    status_color = {
        'completed': '#28a745',
        'failed': '#dc3545',
        'pending': '#ffc107'
    }.get(status, '#6c757d')
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>InfraOps Execution Report - {execution_id}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
            .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            .header {{ border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }}
            .status {{ display: inline-block; padding: 5px 10px; border-radius: 4px; color: white; background-color: {status_color}; }}
            .section {{ margin: 20px 0; }}
            .section h3 {{ color: #495057; border-bottom: 1px solid #dee2e6; padding-bottom: 5px; }}
            .info-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
            .info-box {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; }}
            .error {{ background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; }}
            .success {{ background-color: #d4edda; color: #155724; padding: 10px; border-radius: 4px; }}
            pre {{ background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6; }}
            th {{ background-color: #e9ecef; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 InfraOps Execution Report</h1>
                <p><strong>Execution ID:</strong> {execution_id}</p>
                <p><strong>Status:</strong> <span class="status">{status.upper()}</span></p>
                <p><strong>Duration:</strong> {start_time} - {end_time}</p>
            </div>
    """
    
    # Add infrastructure information if available
    if 'infrastructure' in data:
        html += _generate_infrastructure_section(data['infrastructure'])
    
    # Add execution phases
    html += _generate_phases_section(data)
    
    # Add errors if any
    if data.get('errors'):
        html += f"""
            <div class="section">
                <h3>❌ Errors</h3>
                <div class="error">
                    <ul>
                        {''.join(f'<li>{error}</li>' for error in data['errors'])}
                    </ul>
                </div>
            </div>
        """
    
    html += """
        </div>
    </body>
    </html>
    """
    
    return html


def _generate_infrastructure_section(infra_data: Dict[str, Any]) -> str:
    """Generate infrastructure section for HTML report."""
    if not infra_data:
        return ""
    
    html = """
        <div class="section">
            <h3>🏗️ Infrastructure</h3>
            <div class="info-grid">
    """
    
    # Add infrastructure details
    if 'infrastructure_info' in infra_data:
        info = infra_data['infrastructure_info'].get('value', {})
        
        if 'instances' in info:
            for name, instance in info['instances'].items():
                html += f"""
                    <div class="info-box">
                        <h4>{name}</h4>
                        <p><strong>Instance ID:</strong> {instance.get('id', 'N/A')}</p>
                        <p><strong>Public IP:</strong> {instance.get('public_ip', 'N/A')}</p>
                        <p><strong>Private IP:</strong> {instance.get('private_ip', 'N/A')}</p>
                        <p><strong>Type:</strong> {instance.get('type', 'N/A')}</p>
                        <p><strong>AZ:</strong> {instance.get('az', 'N/A')}</p>
                    </div>
                """
    
    html += """
            </div>
        </div>
    """
    
    return html


def _generate_phases_section(data: Dict[str, Any]) -> str:
    """Generate execution phases section for HTML report."""
    html = """
        <div class="section">
            <h3>📋 Execution Phases</h3>
            <table>
                <tr>
                    <th>Phase</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
    """
    
    # Terraform phase
    tf_status = data.get('terraform', {}).get('status', 'unknown')
    tf_class = 'success' if tf_status == 'completed' else 'error'
    html += f"""
        <tr>
            <td>Terraform</td>
            <td><span class="{tf_class}">{tf_status}</span></td>
            <td>Infrastructure provisioning</td>
        </tr>
    """
    
    # Ansible phase
    ansible_status = data.get('ansible', {}).get('status', 'unknown')
    ansible_class = 'success' if ansible_status == 'completed' else 'error'
    html += f"""
        <tr>
            <td>Ansible</td>
            <td><span class="{ansible_class}">{ansible_status}</span></td>
            <td>Configuration management</td>
        </tr>
    """
    
    html += """
            </table>
        </div>
    """
    
    return html


def _generate_markdown_report(data: Dict[str, Any]) -> str:
    """Generate markdown report content."""
    execution_id = data.get('execution_id', 'unknown')
    status = data.get('status', 'unknown')
    start_time = data.get('start_time', 'unknown')
    end_time = data.get('end_time', 'unknown')
    
    status_emoji = {
        'completed': '✅',
        'failed': '❌',
        'pending': '⏳'
    }.get(status, '❓')
    
    md = f"""# InfraOps Execution Report

## Summary
- **Execution ID:** {execution_id}
- **Status:** {status_emoji} {status.upper()}
- **Start Time:** {start_time}
- **End Time:** {end_time}

## Execution Phases

### Terraform
- **Status:** {data.get('terraform', {}).get('status', 'unknown')}
- **Purpose:** Infrastructure provisioning

### Ansible
- **Status:** {data.get('ansible', {}).get('status', 'unknown')}
- **Purpose:** Configuration management

"""
    
    # Add infrastructure information
    if 'infrastructure' in data and 'infrastructure_info' in data['infrastructure']:
        md += "## Infrastructure\n\n"
        info = data['infrastructure']['infrastructure_info'].get('value', {})
        
        if 'instances' in info:
            for name, instance in info['instances'].items():
                md += f"""### {name}
- **Instance ID:** {instance.get('id', 'N/A')}
- **Public IP:** {instance.get('public_ip', 'N/A')}
- **Private IP:** {instance.get('private_ip', 'N/A')}
- **Type:** {instance.get('type', 'N/A')}
- **Availability Zone:** {instance.get('az', 'N/A')}

"""
    
    # Add errors if any
    if data.get('errors'):
        md += "## Errors\n\n"
        for error in data['errors']:
            md += f"- {error}\n"
        md += "\n"
    
    md += f"""## Next Steps

1. **Access your server:**
   ```bash
   ssh -i ~/.ssh/infraops_key ubuntu@<public_ip>
   ```

2. **Check services:**
   ```bash
   sudo systemctl status nginx docker
   ```

3. **View web server:**
   Open http://<public_ip> in your browser

---
*Report generated on {datetime.now().isoformat()}*
"""
    
    return md


def validate_ssh_key(key_path: str) -> bool:
    """Validate SSH key file exists and has correct permissions."""
    key_file = Path(key_path)
    
    if not key_file.exists():
        logging.error(f"SSH key file not found: {key_path}")
        return False
    
    # Check permissions (should be 600 or 400)
    stat = key_file.stat()
    mode = oct(stat.st_mode)[-3:]
    
    if mode not in ['600', '400']:
        logging.warning(f"SSH key has incorrect permissions: {mode}. Should be 600 or 400.")
        logging.info(f"Fix with: chmod 600 {key_path}")
    
    return True


def get_public_ip() -> Optional[str]:
    """Get the current public IP address."""
    import requests
    
    try:
        response = requests.get('https://ifconfig.me', timeout=10)
        if response.status_code == 200:
            return response.text.strip()
    except Exception:
        pass
    
    try:
        response = requests.get('https://api.ipify.org', timeout=10)
        if response.status_code == 200:
            return response.text.strip()
    except Exception:
        pass
    
    logging.warning("Could not determine public IP address")
    return None
