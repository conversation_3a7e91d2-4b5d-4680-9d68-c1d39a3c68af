#!/usr/bin/env python3
"""
Terraform execution wrapper for InfraOps pipeline.
"""

import os
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)


class TerraformRunner:
    """Wrapper for Terraform operations."""
    
    def __init__(self, terraform_dir: Path, artifacts_dir: Path):
        self.terraform_dir = Path(terraform_dir)
        self.artifacts_dir = Path(artifacts_dir)
        self.terraform_bin = self._find_terraform_binary()
        
        # Ensure directories exist
        self.terraform_dir.mkdir(parents=True, exist_ok=True)
        self.artifacts_dir.mkdir(parents=True, exist_ok=True)
    
    def _find_terraform_binary(self) -> str:
        """Find Terraform binary in PATH."""
        try:
            result = subprocess.run(['which', 'terraform'], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            # Try common locations
            common_paths = ['/usr/local/bin/terraform', '/usr/bin/terraform']
            for path in common_paths:
                if os.path.exists(path):
                    return path
            raise RuntimeError("Terraform binary not found in PATH")
    
    def _run_terraform_command(self, args: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run a Terraform command with proper error handling."""
        cmd = [self.terraform_bin] + args
        logger.debug(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.terraform_dir,
                capture_output=capture_output,
                text=True,
                check=False  # We'll handle return codes manually
            )
            
            if result.returncode != 0:
                logger.error(f"Terraform command failed: {' '.join(cmd)}")
                logger.error(f"STDOUT: {result.stdout}")
                logger.error(f"STDERR: {result.stderr}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute Terraform command: {str(e)}")
            raise
    
    def init(self) -> bool:
        """Initialize Terraform working directory."""
        logger.info("Initializing Terraform...")
        
        result = self._run_terraform_command(['init', '-no-color'])
        
        if result.returncode == 0:
            logger.info("Terraform initialization completed successfully")
            return True
        else:
            logger.error("Terraform initialization failed")
            return False
    
    def plan(self, var_file: Optional[str] = None) -> Optional[str]:
        """Generate and return Terraform execution plan."""
        logger.info("Generating Terraform plan...")
        
        args = ['plan', '-no-color', '-detailed-exitcode']
        
        if var_file:
            args.extend(['-var-file', var_file])
        
        # Check for terraform.tfvars file
        tfvars_path = self.terraform_dir / 'terraform.tfvars'
        if tfvars_path.exists():
            args.extend(['-var-file', str(tfvars_path)])
        
        result = self._run_terraform_command(args)
        
        # Save plan output to artifacts
        plan_file = self.artifacts_dir / 'terraform_plan.txt'
        with open(plan_file, 'w') as f:
            f.write(result.stdout)
            if result.stderr:
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
        
        if result.returncode in [0, 2]:  # 0 = no changes, 2 = changes present
            logger.info("Terraform plan generated successfully")
            return result.stdout
        else:
            logger.error("Terraform plan generation failed")
            return None
    
    def apply(self, auto_approve: bool = False) -> bool:
        """Apply Terraform configuration."""
        logger.info("Applying Terraform configuration...")
        
        args = ['apply', '-no-color']
        
        if auto_approve:
            args.append('-auto-approve')
        
        # Check for terraform.tfvars file
        tfvars_path = self.terraform_dir / 'terraform.tfvars'
        if tfvars_path.exists():
            args.extend(['-var-file', str(tfvars_path)])
        
        result = self._run_terraform_command(args, capture_output=False)
        
        # Save apply output to artifacts
        apply_file = self.artifacts_dir / 'terraform_apply.txt'
        with open(apply_file, 'w') as f:
            f.write(f"Return code: {result.returncode}\n")
            if hasattr(result, 'stdout') and result.stdout:
                f.write(result.stdout)
            if hasattr(result, 'stderr') and result.stderr:
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
        
        if result.returncode == 0:
            logger.info("Terraform apply completed successfully")
            return True
        else:
            logger.error("Terraform apply failed")
            return False
    
    def get_outputs(self) -> Optional[Dict]:
        """Get Terraform outputs as JSON."""
        logger.info("Retrieving Terraform outputs...")
        
        result = self._run_terraform_command(['output', '-json'])
        
        if result.returncode == 0:
            try:
                outputs = json.loads(result.stdout)
                
                # Save outputs to artifacts
                outputs_file = self.artifacts_dir / 'terraform_outputs.json'
                with open(outputs_file, 'w') as f:
                    json.dump(outputs, f, indent=2)
                
                logger.info("Terraform outputs retrieved successfully")
                return outputs
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Terraform outputs JSON: {str(e)}")
                return None
        else:
            logger.error("Failed to retrieve Terraform outputs")
            return None
    
    def destroy(self, auto_approve: bool = False) -> bool:
        """Destroy Terraform-managed infrastructure."""
        logger.warning("Destroying Terraform infrastructure...")
        
        args = ['destroy', '-no-color']
        
        if auto_approve:
            args.append('-auto-approve')
        
        # Check for terraform.tfvars file
        tfvars_path = self.terraform_dir / 'terraform.tfvars'
        if tfvars_path.exists():
            args.extend(['-var-file', str(tfvars_path)])
        
        result = self._run_terraform_command(args, capture_output=False)
        
        if result.returncode == 0:
            logger.info("Terraform destroy completed successfully")
            return True
        else:
            logger.error("Terraform destroy failed")
            return False
    
    def validate(self) -> bool:
        """Validate Terraform configuration."""
        logger.info("Validating Terraform configuration...")
        
        result = self._run_terraform_command(['validate', '-no-color'])
        
        if result.returncode == 0:
            logger.info("Terraform configuration is valid")
            return True
        else:
            logger.error("Terraform configuration validation failed")
            logger.error(result.stdout)
            return False
    
    def format_check(self) -> bool:
        """Check if Terraform files are properly formatted."""
        logger.info("Checking Terraform formatting...")
        
        result = self._run_terraform_command(['fmt', '-check', '-diff'])
        
        if result.returncode == 0:
            logger.info("Terraform files are properly formatted")
            return True
        else:
            logger.warning("Terraform files need formatting")
            logger.info("Run 'terraform fmt' to fix formatting issues")
            return False
