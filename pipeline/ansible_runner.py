#!/usr/bin/env python3
"""
Ansible execution wrapper for InfraOps pipeline.
"""

import os
import logging
import subprocess
from pathlib import Path
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)


class AnsibleRunner:
    """Wrapper for Ansible operations."""
    
    def __init__(self, ansible_dir: Path, artifacts_dir: Path):
        self.ansible_dir = Path(ansible_dir)
        self.artifacts_dir = Path(artifacts_dir)
        self.playbooks_dir = self.ansible_dir / 'playbooks'
        self.ansible_playbook_bin = self._find_ansible_binary()
        
        # Ensure directories exist
        self.ansible_dir.mkdir(parents=True, exist_ok=True)
        self.artifacts_dir.mkdir(parents=True, exist_ok=True)
    
    def _find_ansible_binary(self) -> str:
        """Find ansible-playbook binary in PATH."""
        try:
            result = subprocess.run(['which', 'ansible-playbook'], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            # Try common locations
            common_paths = ['/usr/local/bin/ansible-playbook', '/usr/bin/ansible-playbook']
            for path in common_paths:
                if os.path.exists(path):
                    return path
            raise RuntimeError("ansible-playbook binary not found in PATH")
    
    def _run_ansible_command(self, args: List[str], capture_output: bool = False) -> subprocess.CompletedProcess:
        """Run an Ansible command with proper error handling."""
        cmd = [self.ansible_playbook_bin] + args
        logger.debug(f"Running command: {' '.join(cmd)}")
        
        try:
            # Set environment variables for Ansible
            env = os.environ.copy()
            env.update({
                'ANSIBLE_HOST_KEY_CHECKING': 'False',
                'ANSIBLE_STDOUT_CALLBACK': 'yaml',
                'ANSIBLE_STDERR_CALLBACK': 'yaml',
                'ANSIBLE_FORCE_COLOR': 'true'
            })
            
            result = subprocess.run(
                cmd,
                cwd=self.ansible_dir,
                capture_output=capture_output,
                text=True,
                env=env,
                check=False  # We'll handle return codes manually
            )
            
            if result.returncode != 0:
                logger.error(f"Ansible command failed: {' '.join(cmd)}")
                if capture_output:
                    logger.error(f"STDOUT: {result.stdout}")
                    logger.error(f"STDERR: {result.stderr}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute Ansible command: {str(e)}")
            raise
    
    def run_playbook(self, playbook_name: str, inventory_path: str, 
                    extra_vars: Optional[Dict] = None, tags: Optional[List[str]] = None,
                    limit: Optional[str] = None) -> bool:
        """Run an Ansible playbook."""
        playbook_path = self.playbooks_dir / playbook_name
        
        if not playbook_path.exists():
            logger.error(f"Playbook not found: {playbook_path}")
            return False
        
        logger.info(f"Running Ansible playbook: {playbook_name}")
        
        args = [
            str(playbook_path),
            '-i', inventory_path,
            '-v'  # Verbose output
        ]
        
        # Add extra variables
        if extra_vars:
            for key, value in extra_vars.items():
                args.extend(['-e', f'{key}={value}'])
        
        # Add tags filter
        if tags:
            args.extend(['--tags', ','.join(tags)])
        
        # Add host limit
        if limit:
            args.extend(['--limit', limit])
        
        # Run the playbook
        result = self._run_ansible_command(args, capture_output=True)
        
        # Save playbook output to artifacts
        output_file = self.artifacts_dir / f'ansible_{playbook_name.replace(".yml", "")}.log'
        with open(output_file, 'w') as f:
            f.write(f"Playbook: {playbook_name}\n")
            f.write(f"Return code: {result.returncode}\n")
            f.write("="*80 + "\n")
            f.write("STDOUT:\n")
            f.write(result.stdout)
            if result.stderr:
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
        
        if result.returncode == 0:
            logger.info(f"Playbook {playbook_name} completed successfully")
            return True
        else:
            logger.error(f"Playbook {playbook_name} failed")
            return False
    
    def check_connectivity(self, inventory_path: str) -> bool:
        """Check connectivity to all hosts in inventory."""
        logger.info("Checking Ansible connectivity...")
        
        args = [
            '-i', inventory_path,
            '-m', 'ping',
            'all'
        ]
        
        result = self._run_ansible_command(args, capture_output=True)
        
        if result.returncode == 0:
            logger.info("Ansible connectivity check passed")
            return True
        else:
            logger.error("Ansible connectivity check failed")
            logger.error(result.stdout)
            return False
    
    def gather_facts(self, inventory_path: str, host_pattern: str = 'all') -> Optional[Dict]:
        """Gather facts from hosts."""
        logger.info(f"Gathering facts from {host_pattern}...")
        
        args = [
            '-i', inventory_path,
            '-m', 'setup',
            host_pattern
        ]
        
        result = self._run_ansible_command(args, capture_output=True)
        
        if result.returncode == 0:
            # Save facts to artifacts
            facts_file = self.artifacts_dir / 'ansible_facts.log'
            with open(facts_file, 'w') as f:
                f.write(result.stdout)
            
            logger.info("Facts gathered successfully")
            return {"stdout": result.stdout}
        else:
            logger.error("Failed to gather facts")
            return None
    
    def run_ad_hoc_command(self, inventory_path: str, module: str, 
                          args: str = "", host_pattern: str = 'all') -> bool:
        """Run an ad-hoc Ansible command."""
        logger.info(f"Running ad-hoc command: {module} {args}")
        
        cmd_args = [
            '-i', inventory_path,
            '-m', module,
            host_pattern
        ]
        
        if args:
            cmd_args.extend(['-a', args])
        
        result = self._run_ansible_command(cmd_args, capture_output=True)
        
        # Save command output to artifacts
        output_file = self.artifacts_dir / f'ansible_adhoc_{module}.log'
        with open(output_file, 'w') as f:
            f.write(f"Module: {module}\n")
            f.write(f"Args: {args}\n")
            f.write(f"Host pattern: {host_pattern}\n")
            f.write(f"Return code: {result.returncode}\n")
            f.write("="*80 + "\n")
            f.write(result.stdout)
            if result.stderr:
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
        
        if result.returncode == 0:
            logger.info(f"Ad-hoc command completed successfully")
            return True
        else:
            logger.error(f"Ad-hoc command failed")
            return False
    
    def validate_playbook(self, playbook_name: str) -> bool:
        """Validate an Ansible playbook syntax."""
        playbook_path = self.playbooks_dir / playbook_name
        
        if not playbook_path.exists():
            logger.error(f"Playbook not found: {playbook_path}")
            return False
        
        logger.info(f"Validating playbook: {playbook_name}")
        
        args = [
            str(playbook_path),
            '--syntax-check'
        ]
        
        result = self._run_ansible_command(args, capture_output=True)
        
        if result.returncode == 0:
            logger.info(f"Playbook {playbook_name} syntax is valid")
            return True
        else:
            logger.error(f"Playbook {playbook_name} syntax validation failed")
            logger.error(result.stdout)
            return False
    
    def dry_run_playbook(self, playbook_name: str, inventory_path: str) -> bool:
        """Run a playbook in check mode (dry run)."""
        playbook_path = self.playbooks_dir / playbook_name
        
        if not playbook_path.exists():
            logger.error(f"Playbook not found: {playbook_path}")
            return False
        
        logger.info(f"Running dry run for playbook: {playbook_name}")
        
        args = [
            str(playbook_path),
            '-i', inventory_path,
            '--check',
            '--diff',
            '-v'
        ]
        
        result = self._run_ansible_command(args, capture_output=True)
        
        # Save dry run output to artifacts
        output_file = self.artifacts_dir / f'ansible_{playbook_name.replace(".yml", "")}_dryrun.log'
        with open(output_file, 'w') as f:
            f.write(f"Dry run for playbook: {playbook_name}\n")
            f.write(f"Return code: {result.returncode}\n")
            f.write("="*80 + "\n")
            f.write(result.stdout)
            if result.stderr:
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
        
        if result.returncode == 0:
            logger.info(f"Dry run for {playbook_name} completed successfully")
            return True
        else:
            logger.error(f"Dry run for {playbook_name} failed")
            return False
