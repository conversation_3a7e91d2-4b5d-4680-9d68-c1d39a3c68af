#!/usr/bin/env python3
"""
InfraOps AI Automation Agent - Main Pipeline Orchestrator

This script coordinates the execution of Terraform and Ansible to provision
and configure infrastructure based on natural language prompts.
"""

import os
import sys
import json
import click
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import pipeline modules (will be created)
try:
    from pipeline.terraform_runner import TerraformRunner
    from pipeline.ansible_runner import AnsibleRunner
    from pipeline.utils import setup_logging, create_artifacts_dir, generate_report
except ImportError:
    # Fallback imports for development
    pass

# Configure logging
logger = logging.getLogger(__name__)


class InfraOpsOrchestrator:
    """Main orchestrator for the InfraOps automation pipeline."""
    
    def __init__(self, environment: str, project_root: Path):
        self.environment = environment
        self.project_root = project_root
        self.terraform_dir = project_root / "terraform" / "live" / environment
        self.ansible_dir = project_root / "ansible"
        self.artifacts_dir = project_root / "artifacts"
        
        # Initialize runners
        self.terraform_runner = TerraformRunner(self.terraform_dir, self.artifacts_dir)
        self.ansible_runner = AnsibleRunner(self.ansible_dir, self.artifacts_dir)
        
        # Execution state
        self.execution_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.state = {
            "execution_id": self.execution_id,
            "environment": environment,
            "start_time": datetime.now().isoformat(),
            "terraform": {"status": "pending"},
            "ansible": {"status": "pending"},
            "infrastructure": {},
            "errors": []
        }
    
    def run_pipeline(self, prompt: str, auto_approve: bool = False) -> bool:
        """
        Run the complete infrastructure pipeline.
        
        Args:
            prompt: Natural language description of desired infrastructure
            auto_approve: Skip human approval steps if True
            
        Returns:
            bool: True if pipeline completed successfully
        """
        try:
            logger.info(f"Starting InfraOps pipeline execution: {self.execution_id}")
            logger.info(f"Environment: {self.environment}")
            logger.info(f"Prompt: {prompt}")
            
            # Create artifacts directory for this execution
            execution_artifacts_dir = create_artifacts_dir(self.artifacts_dir, self.execution_id)
            
            # Step 1: Process the prompt (placeholder for AI processing)
            self._process_prompt(prompt)
            
            # Step 2: Run Terraform
            if not self._run_terraform_phase(auto_approve):
                return False
            
            # Step 3: Generate Ansible inventory
            if not self._generate_ansible_inventory():
                return False
            
            # Step 4: Run Ansible playbooks
            if not self._run_ansible_phase(auto_approve):
                return False
            
            # Step 5: Generate final report
            self._generate_final_report()
            
            self.state["status"] = "completed"
            self.state["end_time"] = datetime.now().isoformat()
            
            logger.info("Pipeline execution completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {str(e)}")
            self.state["status"] = "failed"
            self.state["errors"].append(str(e))
            self.state["end_time"] = datetime.now().isoformat()
            return False
    
    def _process_prompt(self, prompt: str):
        """Process the natural language prompt (placeholder for AI integration)."""
        logger.info("Processing natural language prompt...")
        
        # TODO: Integrate with AI/LLM to parse prompt and generate configuration
        # For now, we'll use the default configuration
        
        self.state["prompt"] = prompt
        self.state["processed_config"] = {
            "infrastructure_type": "web_server",
            "components": ["ec2", "nginx", "docker"],
            "environment": self.environment
        }
        
        logger.info("Prompt processed successfully")
    
    def _run_terraform_phase(self, auto_approve: bool) -> bool:
        """Run the Terraform provisioning phase."""
        logger.info("Starting Terraform provisioning phase...")
        
        try:
            # Initialize Terraform
            if not self.terraform_runner.init():
                self.state["terraform"]["status"] = "init_failed"
                return False
            
            # Plan Terraform changes
            plan_output = self.terraform_runner.plan()
            if plan_output is None:
                self.state["terraform"]["status"] = "plan_failed"
                return False
            
            # Human approval checkpoint
            if not auto_approve:
                if not self._get_terraform_approval(plan_output):
                    self.state["terraform"]["status"] = "approval_denied"
                    return False
            
            # Apply Terraform changes
            if not self.terraform_runner.apply(auto_approve):
                self.state["terraform"]["status"] = "apply_failed"
                return False
            
            # Get Terraform outputs
            outputs = self.terraform_runner.get_outputs()
            if outputs:
                self.state["infrastructure"] = outputs
                self.state["terraform"]["status"] = "completed"
                logger.info("Terraform phase completed successfully")
                return True
            else:
                self.state["terraform"]["status"] = "outputs_failed"
                return False
                
        except Exception as e:
            logger.error(f"Terraform phase failed: {str(e)}")
            self.state["terraform"]["status"] = "error"
            self.state["errors"].append(f"Terraform error: {str(e)}")
            return False
    
    def _get_terraform_approval(self, plan_output: str) -> bool:
        """Get human approval for Terraform changes."""
        print("\n" + "="*80)
        print("TERRAFORM PLAN REVIEW")
        print("="*80)
        print(plan_output)
        print("="*80)
        
        while True:
            response = input("\nDo you want to apply these Terraform changes? (yes/no): ").lower().strip()
            if response in ['yes', 'y']:
                return True
            elif response in ['no', 'n']:
                print("Terraform apply cancelled by user.")
                return False
            else:
                print("Please enter 'yes' or 'no'")
    
    def _generate_ansible_inventory(self) -> bool:
        """Generate Ansible inventory from Terraform outputs."""
        logger.info("Generating Ansible inventory...")
        
        try:
            if "infrastructure_info" not in self.state["infrastructure"]:
                logger.error("No infrastructure info found in Terraform outputs")
                return False
            
            infra_info = self.state["infrastructure"]["infrastructure_info"]["value"]
            inventory_content = self._create_inventory_content(infra_info)
            
            # Write inventory file
            inventory_path = self.ansible_dir / "inventories" / f"inventory_{self.execution_id}.ini"
            with open(inventory_path, 'w') as f:
                f.write(inventory_content)
            
            self.state["ansible"]["inventory_path"] = str(inventory_path)
            logger.info(f"Ansible inventory generated: {inventory_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate Ansible inventory: {str(e)}")
            self.state["errors"].append(f"Inventory generation error: {str(e)}")
            return False
    
    def _create_inventory_content(self, infra_info: Dict) -> str:
        """Create Ansible inventory content from infrastructure info."""
        inventory_lines = ["# Generated Ansible inventory", ""]
        
        # Add web servers group
        inventory_lines.append("[web_servers]")
        
        if "instances" in infra_info:
            for instance_name, instance_data in infra_info["instances"].items():
                line = f"{instance_name} "
                line += f"ansible_host={instance_data['public_ip']} "
                line += f"ansible_user=ubuntu "
                line += f"instance_id={instance_data['id']} "
                line += f"private_ip={instance_data['private_ip']}"
                inventory_lines.append(line)
        
        # Add group variables
        inventory_lines.extend([
            "",
            "[web_servers:vars]",
            "ansible_python_interpreter=/usr/bin/python3",
            "ansible_ssh_common_args='-o StrictHostKeyChecking=no'",
            "",
            "[all:vars]",
            "ansible_user=ubuntu",
            "ansible_host_key_checking=False",
            "ansible_ssh_retries=3",
            "ansible_ssh_timeout=30"
        ])
        
        return "\n".join(inventory_lines)
    
    def _run_ansible_phase(self, auto_approve: bool) -> bool:
        """Run the Ansible configuration phase."""
        logger.info("Starting Ansible configuration phase...")
        
        try:
            inventory_path = self.state["ansible"]["inventory_path"]
            
            # Human approval checkpoint
            if not auto_approve:
                if not self._get_ansible_approval():
                    self.state["ansible"]["status"] = "approval_denied"
                    return False
            
            # Run playbooks in sequence
            playbooks = ["bootstrap.yml", "harden.yml", "app.yml"]
            
            for playbook in playbooks:
                logger.info(f"Running playbook: {playbook}")
                
                if not self.ansible_runner.run_playbook(playbook, inventory_path):
                    self.state["ansible"]["status"] = f"failed_at_{playbook}"
                    return False
                
                logger.info(f"Playbook {playbook} completed successfully")
            
            self.state["ansible"]["status"] = "completed"
            logger.info("Ansible phase completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Ansible phase failed: {str(e)}")
            self.state["ansible"]["status"] = "error"
            self.state["errors"].append(f"Ansible error: {str(e)}")
            return False
    
    def _get_ansible_approval(self) -> bool:
        """Get human approval for Ansible execution."""
        print("\n" + "="*80)
        print("ANSIBLE CONFIGURATION REVIEW")
        print("="*80)
        print("The following Ansible playbooks will be executed:")
        print("1. bootstrap.yml - Initial server setup and user creation")
        print("2. harden.yml - Security hardening and firewall configuration")
        print("3. app.yml - Application installation (Docker + NGINX)")
        print("="*80)
        
        while True:
            response = input("\nDo you want to run the Ansible configuration? (yes/no): ").lower().strip()
            if response in ['yes', 'y']:
                return True
            elif response in ['no', 'n']:
                print("Ansible execution cancelled by user.")
                return False
            else:
                print("Please enter 'yes' or 'no'")
    
    def _generate_final_report(self):
        """Generate final execution report."""
        logger.info("Generating final report...")
        
        try:
            report_data = {
                "execution_summary": self.state,
                "infrastructure": self.state.get("infrastructure", {}),
                "timestamp": datetime.now().isoformat()
            }
            
            report_path = self.artifacts_dir / f"report_{self.execution_id}.json"
            with open(report_path, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            # Generate human-readable report
            self._generate_markdown_report(report_data)
            
            logger.info(f"Final report generated: {report_path}")
            
        except Exception as e:
            logger.error(f"Failed to generate final report: {str(e)}")
    
    def _generate_markdown_report(self, report_data: Dict):
        """Generate a human-readable markdown report."""
        # Implementation for markdown report generation
        pass


@click.command()
@click.option('--env', default='dev', help='Environment to deploy to (dev/staging/prod)')
@click.option('--prompt', required=True, help='Natural language description of desired infrastructure')
@click.option('--auto-approve', is_flag=True, help='Skip approval prompts')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(env: str, prompt: str, auto_approve: bool, verbose: bool):
    """InfraOps AI Automation Agent - Infrastructure as Code Pipeline"""
    
    # Setup logging
    log_level = logging.DEBUG if verbose else logging.INFO
    setup_logging(log_level)
    
    # Initialize orchestrator
    orchestrator = InfraOpsOrchestrator(env, project_root)
    
    # Run pipeline
    success = orchestrator.run_pipeline(prompt, auto_approve)
    
    if success:
        click.echo(click.style("✅ Pipeline completed successfully!", fg='green'))
        sys.exit(0)
    else:
        click.echo(click.style("❌ Pipeline failed!", fg='red'))
        sys.exit(1)


if __name__ == '__main__':
    main()
